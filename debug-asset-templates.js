/**
 * Debug <PERSON>ript cho Asset Templates Edit Function
 * Chạy script n<PERSON>y trong Browser Console để debug vấn đề
 */

console.log('=== ASSET TEMPLATES DEBUG SCRIPT ===');

// 1. <PERSON><PERSON><PERSON> tra các biến global
console.log('1. Checking Global Variables:');
console.log('- window.assetTemplatesRoutes:', window.assetTemplatesRoutes);
console.log('- window.assetFieldsRoute:', window.assetFieldsRoute);
console.log('- window.contractTypes:', window.contractTypes);

// 2. Ki<PERSON>m tra jQuery và Bootstrap
console.log('\n2. Checking Dependencies:');
console.log('- jQuery version:', typeof $ !== 'undefined' ? $.fn.jquery : 'NOT LOADED');
console.log('- Bootstrap:', typeof bootstrap !== 'undefined' ? 'LOADED' : 'NOT LOADED');

// 3. <PERSON><PERSON><PERSON> tra DataTable
console.log('\n3. Checking DataTable:');
console.log('- DataTables plugin:', typeof $.fn.DataTable !== 'undefined' ? 'LOADED' : 'NOT LOADED');
console.log('- assetTemplatesTable variable:', typeof assetTemplatesTable !== 'undefined' ? 'DEFINED' : 'NOT DEFINED');

// 4. Kiểm tra Modal element
console.log('\n4. Checking Modal:');
const modal = document.getElementById('assetTemplateModal');
console.log('- Modal element exists:', modal !== null);
if (modal) {
  console.log('- Modal classes:', modal.className);
  console.log('- Modal body exists:', modal.querySelector('.modal-body') !== null);
}

// 5. Kiểm tra functions
console.log('\n5. Checking Functions:');
console.log('- editTemplate function:', typeof editTemplate !== 'undefined' ? 'DEFINED' : 'NOT DEFINED');
console.log('- showModalLoading function:', typeof showModalLoading !== 'undefined' ? 'DEFINED' : 'NOT DEFINED');
console.log('- showToast function:', typeof showToast !== 'undefined' ? 'DEFINED' : 'NOT DEFINED');

// 6. Test editTemplate function với ID mẫu
console.log('\n6. Testing editTemplate function:');
if (typeof editTemplate === 'function') {
  console.log('editTemplate function is available. Testing with ID 1...');
  
  // Override AJAX để debug
  const originalAjax = $.ajax;
  $.ajax = function(options) {
    console.log('AJAX Call Details:');
    console.log('- URL:', options.url);
    console.log('- Type:', options.type);
    console.log('- Options:', options);
    
    // Restore original AJAX và thực hiện call
    $.ajax = originalAjax;
    return originalAjax.call(this, {
      ...options,
      success: function(response) {
        console.log('AJAX Success Response:', response);
        if (options.success) options.success(response);
      },
      error: function(xhr, status, error) {
        console.error('AJAX Error Details:');
        console.error('- Status:', xhr.status);
        console.error('- Status Text:', xhr.statusText);
        console.error('- Response Text:', xhr.responseText);
        console.error('- Error:', error);
        if (options.error) options.error(xhr, status, error);
      }
    });
  };
  
  // Test với ID 1 (thay đổi ID này theo dữ liệu thực tế)
  // editTemplate(1);
  console.log('Ready to test. Run: editTemplate(YOUR_TEMPLATE_ID)');
} else {
  console.error('editTemplate function is not defined!');
}

// 7. Kiểm tra routes
console.log('\n7. Testing Routes:');
if (window.assetTemplatesRoutes && typeof window.assetTemplatesRoutes.show === 'function') {
  console.log('- show route for ID 1:', window.assetTemplatesRoutes.show(1));
} else {
  console.error('Routes not properly defined!');
}

// 8. Kiểm tra CSRF token
console.log('\n8. Checking CSRF Token:');
const csrfToken = $('meta[name="csrf-token"]').attr('content');
console.log('- CSRF Token:', csrfToken ? 'FOUND' : 'NOT FOUND');

// 9. Kiểm tra permissions (nếu có)
console.log('\n9. Checking Permissions:');
// Kiểm tra xem user có quyền edit không bằng cách xem button edit có tồn tại
const editButtons = document.querySelectorAll('button[onclick*="editTemplate"]');
console.log('- Edit buttons found:', editButtons.length);

// 10. Manual test function
window.debugEditTemplate = function(id) {
  console.log(`\n=== MANUAL TEST editTemplate(${id}) ===`);
  
  if (!id) {
    console.error('Please provide a template ID');
    return;
  }
  
  // Test route generation
  const url = window.assetTemplatesRoutes.show(id);
  console.log('Generated URL:', url);
  
  // Test AJAX call manually
  $.ajax({
    url: url,
    type: 'GET',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    beforeSend: function() {
      console.log('AJAX request starting...');
    },
    success: function(response) {
      console.log('✅ AJAX Success!');
      console.log('Response:', response);
      
      // Test modal show
      try {
        $('#assetTemplateModal').modal('show');
        console.log('✅ Modal show successful!');
      } catch (e) {
        console.error('❌ Modal show failed:', e);
      }
    },
    error: function(xhr, status, error) {
      console.error('❌ AJAX Failed!');
      console.error('Status:', xhr.status);
      console.error('Response:', xhr.responseText);
      console.error('Error:', error);
    }
  });
};

console.log('\n=== DEBUG COMPLETE ===');
console.log('To test manually, run: debugEditTemplate(YOUR_TEMPLATE_ID)');
console.log('Example: debugEditTemplate(1)');
