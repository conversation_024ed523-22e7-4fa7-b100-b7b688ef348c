<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Modal Timing Issue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .error { color: #dc3545; }
        .warning { color: #fd7e14; }
        .success { color: #198754; }
        .info { color: #0dcaf0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">🔧 Debug Modal Timing Issue</h1>
        
        <!-- Test Controls -->
        <div class="debug-section">
            <h5>Test Controls</h5>
            <div class="row g-2">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary w-100" onclick="testModalShow()">Test Modal Show</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-warning w-100" onclick="testElementSearch()">Test Element Search</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-info w-100" onclick="debugModalState()">Debug Modal State</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearConsole()">Clear Console</button>
                </div>
            </div>
            <div class="row g-2 mt-2">
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-success w-100" onclick="simulateEditField()">Simulate Edit Field</button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="testTimingIssue()">Test Timing Issue</button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-info w-100" onclick="testModalEvents()">Test Modal Events</button>
                </div>
            </div>
        </div>
        
        <!-- Console Output -->
        <div class="debug-section">
            <h5>Console Output</h5>
            <div id="consoleOutput" class="console-output"></div>
        </div>
    </div>

    <!-- Simulated Asset Field Modal -->
    <div class="modal fade" id="assetFieldModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assetFieldModalTitle">Sửa field</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="assetFieldForm" class="field-form-container">
                    <div class="modal-body">
                        <input type="hidden" id="assetFieldId" name="id">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating form-floating-outline">
                                    <input type="text" id="assetFieldName" name="name" class="form-control" required>
                                    <label for="assetFieldName">Tên field (name) *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating form-floating-outline">
                                    <input type="text" id="assetFieldLabel" name="label" class="form-control" required>
                                    <label for="assetFieldLabel">Label hiển thị *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating form-floating-outline">
                                    <select id="assetFieldType" name="type" class="form-select" required>
                                        <option value="">Chọn loại field</option>
                                        <option value="text">Text</option>
                                        <option value="number">Number</option>
                                        <option value="select">Select</option>
                                        <option value="textarea">Textarea</option>
                                        <option value="radio">Radio</option>
                                        <option value="checkbox">Checkbox</option>
                                        <option value="file">File</option>
                                        <option value="date">Date</option>
                                    </select>
                                    <label for="assetFieldType">Loại field *</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Options Section -->
                        <div class="field-options-section mt-4" style="display: none;">
                            <h6>Tùy chọn</h6>
                            <div class="options-container">
                                <!-- Options will be added here -->
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary add-option-btn">
                                <i class="ri-add-line me-1"></i>
                                Thêm tùy chọn
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i>
                            Lưu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
        // Console capture
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warning' : type === 'info' ? 'info' : '';
            consoleOutput.innerHTML += `<span class="${className}">[${timestamp}] ${type.toUpperCase()}: ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        // Test functions
        function debugModalState() {
            console.log('=== MODAL DEBUG INFO ===');
            console.log('Modal exists:', !!document.getElementById('assetFieldModal'));
            console.log('Modal is visible:', $('#assetFieldModal').is(':visible'));
            console.log('Modal has show class:', $('#assetFieldModal').hasClass('show'));
            console.log('assetFieldType element exists:', !!document.getElementById('assetFieldType'));
            console.log('assetFieldType in modal:', !!document.querySelector('#assetFieldModal #assetFieldType'));
            console.log('All select elements in modal:', $('#assetFieldModal').find('select').length);
            console.log('Modal display style:', $('#assetFieldModal').css('display'));
            console.log('=== END MODAL DEBUG ===');
        }
        
        function testModalShow() {
            console.log('=== Testing Modal Show ===');
            debugModalState();
            
            console.log('Showing modal...');
            $('#assetFieldModal').modal('show');
            
            setTimeout(() => {
                console.log('After modal show:');
                debugModalState();
            }, 100);
        }
        
        function testElementSearch() {
            console.log('=== Testing Element Search ===');
            
            const methods = [
                () => document.getElementById('assetFieldType'),
                () => document.querySelector('#assetFieldType'),
                () => document.querySelector('#assetFieldModal [name="type"]'),
                () => document.querySelector('.modal [name="type"]'),
                () => document.querySelector('select[name="type"]'),
                () => $('#assetFieldType')[0],
                () => $('#assetFieldModal').find('[name="type"]')[0]
            ];
            
            const methodNames = [
                'document.getElementById("assetFieldType")',
                'document.querySelector("#assetFieldType")',
                'document.querySelector("#assetFieldModal [name=\\"type\\"]")',
                'document.querySelector(".modal [name=\\"type\\"]")',
                'document.querySelector("select[name=\\"type\\"]")',
                '$("assetFieldType")[0]',
                '$("assetFieldModal").find("[name=\\"type\\"]")[0]'
            ];
            
            methods.forEach((method, index) => {
                try {
                    const result = method();
                    console.log(`${methodNames[index]}: ${result ? 'FOUND' : 'NOT FOUND'}`);
                } catch (error) {
                    console.error(`${methodNames[index]}: ERROR - ${error.message}`);
                }
            });
        }
        
        function testTimingIssue() {
            console.log('=== Testing Timing Issue ===');
            
            // Hide modal first
            $('#assetFieldModal').modal('hide');
            
            setTimeout(() => {
                console.log('Attempting to find element before modal show...');
                testElementSearch();
                
                console.log('Showing modal...');
                $('#assetFieldModal').modal('show');
                
                console.log('Immediately after modal show call...');
                testElementSearch();
                
                setTimeout(() => {
                    console.log('100ms after modal show...');
                    testElementSearch();
                }, 100);
                
                setTimeout(() => {
                    console.log('500ms after modal show...');
                    testElementSearch();
                }, 500);
            }, 500);
        }
        
        function testModalEvents() {
            console.log('=== Testing Modal Events ===');
            
            $('#assetFieldModal').on('show.bs.modal', function() {
                console.log('Modal show.bs.modal event fired');
                testElementSearch();
            });
            
            $('#assetFieldModal').on('shown.bs.modal', function() {
                console.log('Modal shown.bs.modal event fired');
                testElementSearch();
            });
            
            $('#assetFieldModal').modal('show');
        }
        
        function simulateEditField() {
            console.log('=== Simulating Edit Field ===');
            
            // Simulate field data
            const field = {
                id: 1,
                name: 'test_field',
                label: 'Test Field',
                type: 'textarea'
            };
            
            console.log('Field data:', field);
            
            // Show modal first
            $('#assetFieldModal').modal('show');
            
            // Wait for modal to be shown
            $('#assetFieldModal').on('shown.bs.modal.test', function() {
                console.log('Modal fully shown, now setting field values...');
                
                // Remove this specific event listener
                $(this).off('shown.bs.modal.test');
                
                // Set field values
                $('#assetFieldId').val(field.id);
                $('#assetFieldName').val(field.name);
                $('#assetFieldLabel').val(field.label);
                $('#assetFieldType').val(field.type);
                
                console.log('Field values set, now testing element search...');
                testElementSearch();
                
                const selectElement = document.getElementById('assetFieldType');
                if (selectElement) {
                    console.log('SUCCESS: Found assetFieldType element');
                    console.log('Element value:', selectElement.value);
                } else {
                    console.error('FAILED: assetFieldType element not found');
                }
            });
        }
        
        // Auto-run basic test when page loads
        $(document).ready(function() {
            console.log('Page loaded, running initial debug...');
            setTimeout(debugModalState, 100);
        });
    </script>
</body>
</html>
