<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Selector Fixes Test</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
            min-height: 100px;
        }
        .test-result {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0.375rem;
        }
        .test-success {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c2c7;
            color: #842029;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="mb-4">
            <i class="ri-search-line me-2"></i>
            DOM Selector Fixes Test
        </h1>
        
        <div class="alert alert-info">
            <i class="ri-information-line me-2"></i>
            This page tests that JavaScript functions handle missing DOM elements gracefully without console warnings.
        </div>
        
        <!-- Console Monitor -->
        <div class="test-section">
            <h3 class="h5 mb-3">Console Monitor</h3>
            <div id="consoleOutput" class="console-output">
                <div class="text-muted">Console messages will appear here...</div>
            </div>
            <button class="btn btn-secondary btn-sm mt-2" onclick="clearConsole()">Clear Console</button>
        </div>
        
        <!-- Test Results -->
        <div id="testResults" class="mb-4"></div>
        
        <!-- Test 1: Functions Without DOM Elements -->
        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="ri-error-warning-line me-2"></i>
                Test 1: Functions Called Without DOM Elements
            </h2>
            <p class="text-muted">These functions should return silently without console warnings.</p>
            <button class="btn btn-primary mb-2" onclick="testFunctionsWithoutDOM()">
                Test Functions Without DOM Elements
            </button>
            <div id="test1Results"></div>
        </div>
        
        <!-- Test 2: Functions With DOM Elements -->
        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="ri-check-line me-2"></i>
                Test 2: Functions Called With DOM Elements Present
            </h2>
            <p class="text-muted">These functions should work normally when DOM elements exist.</p>
            
            <!-- Mock DOM elements for Asset Templates -->
            <div id="availableFieldsList" class="d-none"></div>
            <div id="templateFieldsList" class="d-none">
                <div id="templateDropZone"></div>
            </div>
            <div id="templatePreview" class="d-none">
                <div id="templatePreviewContent"></div>
            </div>
            
            <!-- Mock DOM elements for Asset Fields -->
            <div class="field-options-section d-none">
                <div class="options-container"></div>
            </div>
            <div class="modal-body d-none"></div>
            
            <button class="btn btn-success mb-2" onclick="testFunctionsWithDOM()">
                Test Functions With DOM Elements
            </button>
            <div id="test2Results"></div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console capture
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        let consoleMessages = [];
        
        function captureConsole() {
            const output = document.getElementById('consoleOutput');
            
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                const message = `[LOG] ${args.join(' ')}`;
                consoleMessages.push({type: 'log', message});
                updateConsoleOutput();
            };
            
            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                const message = `[WARN] ${args.join(' ')}`;
                consoleMessages.push({type: 'warn', message});
                updateConsoleOutput();
            };
            
            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                const message = `[ERROR] ${args.join(' ')}`;
                consoleMessages.push({type: 'error', message});
                updateConsoleOutput();
            };
        }
        
        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            if (consoleMessages.length === 0) {
                output.innerHTML = '<div class="text-muted">No console messages</div>';
            } else {
                output.innerHTML = consoleMessages.map(msg => {
                    const colorClass = msg.type === 'warn' ? 'text-warning' : 
                                     msg.type === 'error' ? 'text-danger' : 'text-info';
                    return `<div class="${colorClass}">${msg.message}</div>`;
                }).join('');
            }
            output.scrollTop = output.scrollHeight;
        }
        
        function clearConsole() {
            consoleMessages = [];
            updateConsoleOutput();
        }
        
        // Mock global variables
        let availableFields = [
            { id: 1, label: 'Test Field 1', type: 'text' },
            { id: 2, label: 'Test Field 2', type: 'select' }
        ];
        let templateFields = [];
        let optionIndex = 0;
        
        // Fixed Asset Templates Functions (with graceful degradation)
        function renderAvailableFields() {
            const container = document.getElementById('availableFieldsList');
            
            if (!container) {
                return; // Silent return - no warning
            }
            
            container.innerHTML = '';
            
            if (availableFields.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">Không có fields nào</p>';
                return;
            }
            
            console.log('renderAvailableFields: Successfully rendered fields');
        }
        
        function renderTemplateFields() {
            const container = document.getElementById('templateFieldsList');
            const dropZone = document.getElementById('templateDropZone');
            
            if (!container || !dropZone) {
                return; // Silent return - no warning
            }
            
            console.log('renderTemplateFields: Successfully processed template fields');
        }
        
        function generateTemplatePreview() {
            const previewContainer = document.getElementById('templatePreview');
            const previewContent = document.getElementById('templatePreviewContent');
            
            if (!previewContainer || !previewContent) {
                return; // Silent return - no warning
            }
            
            console.log('generateTemplatePreview: Successfully generated preview');
        }
        
        // Fixed Asset Fields Functions (with graceful degradation)
        function handleFieldTypeChange(type) {
            const optionsSection = document.querySelector('.field-options-section');
            const optionsContainer = document.querySelector('.options-container');
            
            if (!optionsSection || !optionsContainer) {
                return; // Silent return - no warning
            }
            
            const needsOptions = ['select', 'radio', 'checkbox'].includes(type);
            
            if (needsOptions) {
                optionsSection.style.display = 'block';
            } else {
                optionsSection.style.display = 'none';
                optionsContainer.innerHTML = '';
            }
            
            console.log(`handleFieldTypeChange: Successfully handled type change to ${type}`);
        }
        
        function addFieldOption() {
            const optionsContainer = document.querySelector('.options-container');
            
            if (!optionsContainer) {
                return; // Silent return - no warning
            }
            
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.innerHTML = `<input type="text" placeholder="Option ${optionIndex}">`;
            
            optionsContainer.appendChild(optionDiv);
            optionIndex++;
            
            console.log('addFieldOption: Successfully added field option');
        }
        
        // Test functions
        function testFunctionsWithoutDOM() {
            const results = document.getElementById('test1Results');
            results.innerHTML = '<h6>Testing Functions Without DOM Elements:</h6>';
            
            const initialMessageCount = consoleMessages.length;
            
            try {
                // Hide all DOM elements
                document.getElementById('availableFieldsList').style.display = 'none';
                document.getElementById('templateFieldsList').style.display = 'none';
                document.querySelector('.field-options-section').style.display = 'none';
                
                // Call functions that should return silently
                renderAvailableFields();
                renderTemplateFields();
                generateTemplatePreview();
                handleFieldTypeChange('text');
                addFieldOption();
                
                const finalMessageCount = consoleMessages.length;
                const newMessages = finalMessageCount - initialMessageCount;
                
                if (newMessages === 0) {
                    results.innerHTML += '<div class="test-result test-success">✅ All functions returned silently - No console warnings!</div>';
                } else {
                    results.innerHTML += `<div class="test-result test-error">❌ Functions generated ${newMessages} console messages</div>`;
                }
                
            } catch (error) {
                results.innerHTML += `<div class="test-result test-error">❌ Unexpected error: ${error.message}</div>`;
            }
        }
        
        function testFunctionsWithDOM() {
            const results = document.getElementById('test2Results');
            results.innerHTML = '<h6>Testing Functions With DOM Elements:</h6>';
            
            try {
                // Show DOM elements
                document.getElementById('availableFieldsList').style.display = 'block';
                document.getElementById('templateFieldsList').style.display = 'block';
                document.querySelector('.field-options-section').style.display = 'block';
                
                const initialMessageCount = consoleMessages.length;
                
                // Call functions that should work normally
                renderAvailableFields();
                renderTemplateFields();
                generateTemplatePreview();
                handleFieldTypeChange('select');
                addFieldOption();
                
                const finalMessageCount = consoleMessages.length;
                const newMessages = finalMessageCount - initialMessageCount;
                
                if (newMessages > 0) {
                    results.innerHTML += `<div class="test-result test-success">✅ Functions executed successfully - Generated ${newMessages} log messages</div>`;
                } else {
                    results.innerHTML += '<div class="test-result test-error">❌ Functions did not execute properly</div>';
                }
                
                // Hide elements again
                document.getElementById('availableFieldsList').style.display = 'none';
                document.getElementById('templateFieldsList').style.display = 'none';
                document.querySelector('.field-options-section').style.display = 'none';
                
            } catch (error) {
                results.innerHTML += `<div class="test-result test-error">❌ Unexpected error: ${error.message}</div>`;
            }
        }
        
        // Initialize console capture
        captureConsole();
        
        // Auto-run test on page load
        $(document).ready(function() {
            console.log('DOM Selector Fixes Test - Page loaded');
            
            setTimeout(() => {
                testFunctionsWithoutDOM();
            }, 1000);
        });
    </script>
</body>
</html>
