/**
 * Fix Script cho Asset Templates Edit Function
 * <PERSON><PERSON><PERSON> sửa chữa phổ biến cho vấn đề edit template
 */

// 1. Enhanced editTemplate function với better error handling
function enhancedEditTemplate(id) {
  console.log(`Starting editTemplate for ID: ${id}`);
  
  // Validate ID
  if (!id || isNaN(id)) {
    console.error('Invalid template ID:', id);
    showToast('error', 'ID template không hợp lệ');
    return;
  }
  
  // Check if routes are defined
  if (!window.assetTemplatesRoutes || typeof window.assetTemplatesRoutes.show !== 'function') {
    console.error('Routes not properly defined');
    showToast('error', 'Lỗi cấu hình: Routes không được định nghĩa');
    return;
  }
  
  // Set edit mode
  isEditMode = true;
  $('#assetTemplateModalTitle').text('Sửa template');
  
  // Show modal first, then load data
  $('#assetTemplateModal').modal('show');
  
  // Show loading in modal
  showModalLoading(true);
  
  const url = window.assetTemplatesRoutes.show(id);
  console.log('Making AJAX request to:', url);
  
  $.ajax({
    url: url,
    type: 'GET',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    timeout: 10000, // 10 second timeout
    success: function(response) {
      console.log('AJAX Success:', response);
      showModalLoading(false);
      
      try {
        const template = response.template;
        
        if (!template) {
          throw new Error('Template data not found in response');
        }
        
        // Populate form fields
        $('#assetTemplateId').val(template.id);
        $('#assetTemplateContractType').val(template.contract_type_id);
        $('#assetTemplateName').val(template.name);
        $('#assetTemplateDescription').val(template.description || '');
        $('#assetTemplateSortOrder').val(template.sort_order || 0);
        $('#assetTemplateIsDefault').prop('checked', Boolean(template.is_default));
        $('#assetTemplateIsActive').prop('checked', Boolean(template.is_active));
        
        // Load template fields if available
        if (template.asset_fields && Array.isArray(template.asset_fields)) {
          templateFields = template.asset_fields.map(field => ({
            ...field,
            group_name: field.pivot ? field.pivot.group_name : 'Thông tin cơ bản',
            sort_order: field.pivot ? field.pivot.sort_order : 0,
            is_required: field.pivot ? Boolean(field.pivot.is_required) : false
          }));
          
          // Render template fields if function exists
          if (typeof renderTemplateFields === 'function') {
            renderTemplateFields();
          }
        }
        
        console.log('Form populated successfully');
        showToast('success', 'Đã tải thông tin template');
        
      } catch (error) {
        console.error('Error processing response:', error);
        showToast('error', 'Lỗi xử lý dữ liệu: ' + error.message);
        $('#assetTemplateModal').modal('hide');
      }
    },
    error: function(xhr, status, error) {
      console.error('AJAX Error:', {
        status: xhr.status,
        statusText: xhr.statusText,
        responseText: xhr.responseText,
        error: error
      });
      
      showModalLoading(false);
      
      let errorMessage = 'Không thể tải thông tin template';
      
      if (xhr.status === 403) {
        errorMessage = 'Bạn không có quyền xem template này';
      } else if (xhr.status === 404) {
        errorMessage = 'Template không tồn tại';
      } else if (xhr.status === 500) {
        errorMessage = 'Lỗi server, vui lòng thử lại sau';
      } else if (status === 'timeout') {
        errorMessage = 'Timeout: Yêu cầu mất quá nhiều thời gian';
      }
      
      showToast('error', errorMessage);
      $('#assetTemplateModal').modal('hide');
    }
  });
}

// 2. Enhanced showModalLoading function
function enhancedShowModalLoading(show) {
  const modal = document.getElementById('assetTemplateModal');
  if (!modal) {
    console.error('Modal not found');
    return;
  }
  
  const modalBody = modal.querySelector('.modal-body');
  if (!modalBody) {
    console.error('Modal body not found');
    return;
  }
  
  if (show) {
    // Store original content
    if (!modal.dataset.originalContent) {
      modal.dataset.originalContent = modalBody.innerHTML;
    }
    
    const loadingHtml = `
      <div class="d-flex justify-content-center align-items-center p-5">
        <div class="spinner-border text-primary me-3" role="status">
          <span class="visually-hidden">Đang tải...</span>
        </div>
        <span>Đang tải thông tin template...</span>
      </div>
    `;
    modalBody.innerHTML = loadingHtml;
  } else {
    // Restore original content
    if (modal.dataset.originalContent) {
      modalBody.innerHTML = modal.dataset.originalContent;
    }
  }
}

// 3. Enhanced showToast function (fallback if not available)
function enhancedShowToast(type, message) {
  // Try to use existing showToast function
  if (typeof showToast === 'function') {
    return showToast(type, message);
  }
  
  // Fallback implementation
  console.log(`Toast [${type}]:`, message);
  
  // Try to use Bootstrap toast
  const toastHtml = `
    <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
      <div class="d-flex">
        <div class="toast-body">${message}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;
  
  const toastElement = $(toastHtml);
  $('body').append(toastElement);
  
  if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
    const toast = new bootstrap.Toast(toastElement[0]);
    toast.show();
    
    toastElement.on('hidden.bs.toast', function() {
      $(this).remove();
    });
  } else {
    // Fallback to alert
    alert(`[${type.toUpperCase()}] ${message}`);
    toastElement.remove();
  }
}

// 4. Fix DataTable action buttons
function fixDataTableActions() {
  // Re-initialize DataTable with proper action column
  if (typeof assetTemplatesTable !== 'undefined' && assetTemplatesTable) {
    assetTemplatesTable.ajax.reload(null, false);
  }
}

// 5. Fix modal initialization
function fixModalInitialization() {
  const modal = document.getElementById('assetTemplateModal');
  if (!modal) {
    console.error('Modal element not found');
    return false;
  }
  
  // Ensure modal can be shown
  $(modal).off('shown.bs.modal').on('shown.bs.modal', function() {
    console.log('Modal shown successfully');
    
    // Initialize any components that need to be initialized when modal is shown
    if (typeof renderAvailableFields === 'function') {
      renderAvailableFields();
    }
    if (typeof renderTemplateFields === 'function') {
      renderTemplateFields();
    }
  });
  
  $(modal).off('hidden.bs.modal').on('hidden.bs.modal', function() {
    console.log('Modal hidden');
    
    // Reset form when modal is hidden
    if (typeof resetForm === 'function') {
      resetForm();
    }
  });
  
  return true;
}

// 6. Apply all fixes
function applyAllFixes() {
  console.log('Applying all fixes...');
  
  // Override functions with enhanced versions
  if (typeof window !== 'undefined') {
    window.editTemplate = enhancedEditTemplate;
    window.showModalLoading = enhancedShowModalLoading;
    
    if (typeof showToast === 'undefined') {
      window.showToast = enhancedShowToast;
    }
  }
  
  // Fix modal initialization
  fixModalInitialization();
  
  console.log('All fixes applied successfully');
}

// Auto-apply fixes when script loads
$(document).ready(function() {
  applyAllFixes();
});

// Export for manual use
window.applyAssetTemplatesFixes = applyAllFixes;
window.debugEditTemplate = enhancedEditTemplate;

console.log('Asset Templates Fix Script Loaded');
console.log('Run applyAssetTemplatesFixes() to apply all fixes');
console.log('Run debugEditTemplate(id) to test edit function');
