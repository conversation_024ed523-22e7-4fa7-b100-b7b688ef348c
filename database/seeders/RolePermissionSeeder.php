<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User management
            ['name' => 'users.view', 'guard_name' => 'web'],
            ['name' => 'users.create', 'guard_name' => 'web'],
            ['name' => 'users.edit', 'guard_name' => 'web'],
            ['name' => 'users.delete', 'guard_name' => 'web'],

            // Document management
            ['name' => 'documents.view', 'guard_name' => 'web'],
            ['name' => 'documents.create', 'guard_name' => 'web'],
            ['name' => 'documents.edit', 'guard_name' => 'web'],
            ['name' => 'documents.delete', 'guard_name' => 'web'],

            // Notary management
            ['name' => 'notaries.view', 'guard_name' => 'web'],
            ['name' => 'notaries.create', 'guard_name' => 'web'],
            ['name' => 'notaries.edit', 'guard_name' => 'web'],
            ['name' => 'notaries.delete', 'guard_name' => 'web'],

            // Documents management
            ['name' => 'documents.view', 'guard_name' => 'web'],
            ['name' => 'documents.create', 'guard_name' => 'web'],
            ['name' => 'documents.edit', 'guard_name' => 'web'],
            ['name' => 'documents.delete', 'guard_name' => 'web'],
            ['name' => 'documents.export', 'guard_name' => 'web'],

            // Asset Templates management
            ['name' => 'asset-templates.view', 'guard_name' => 'web'],
            ['name' => 'asset-templates.create', 'guard_name' => 'web'],
            ['name' => 'asset-templates.edit', 'guard_name' => 'web'],
            ['name' => 'asset-templates.delete', 'guard_name' => 'web'],

            // Asset Fields management
            ['name' => 'asset-fields.view', 'guard_name' => 'web'],
            ['name' => 'asset-fields.create', 'guard_name' => 'web'],
            ['name' => 'asset-fields.edit', 'guard_name' => 'web'],
            ['name' => 'asset-fields.delete', 'guard_name' => 'web'],

            // Contract Types management
            ['name' => 'contract-types.view', 'guard_name' => 'web'],
            ['name' => 'contract-types.create', 'guard_name' => 'web'],
            ['name' => 'contract-types.edit', 'guard_name' => 'web'],
            ['name' => 'contract-types.delete', 'guard_name' => 'web'],

            // Role management
            ['name' => 'roles.view', 'guard_name' => 'web'],
            ['name' => 'roles.create', 'guard_name' => 'web'],
            ['name' => 'roles.edit', 'guard_name' => 'web'],
            ['name' => 'roles.delete', 'guard_name' => 'web'],

            // Reports
            ['name' => 'reports.view', 'guard_name' => 'web'],
            ['name' => 'reports.export', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }

        // Create roles
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'web'
        ]);

        $adminRole = Role::firstOrCreate([
            'name' => 'Admin',
            'guard_name' => 'web'
        ]);

        $notaryStaffRole = Role::firstOrCreate([
            'name' => 'Notary Staff',
            'guard_name' => 'web'
        ]);

        $viewerRole = Role::firstOrCreate([
            'name' => 'Viewer',
            'guard_name' => 'web'
        ]);

        // Assign permissions to roles
        // Super Admin gets all permissions
        $allPermissions = Permission::all();
        $superAdminRole->permissions()->sync($allPermissions->pluck('id'));

        // Admin gets most permissions except role management
        $adminPermissions = Permission::whereNotIn('name', [
            'roles.create', 'roles.edit', 'roles.delete'
        ])->get();
        $adminRole->permissions()->sync($adminPermissions->pluck('id'));

        // Notary Staff gets document and notary permissions
        $staffPermissions = Permission::whereIn('name', [
            'documents.view', 'documents.create', 'documents.edit', 'documents.export',
            'notaries.view', 'notaries.create', 'notaries.edit',
            'asset-templates.view', 'asset-fields.view', 'contract-types.view'
        ])->get();
        $notaryStaffRole->permissions()->sync($staffPermissions->pluck('id'));

        // Viewer gets only view permissions
        $viewerPermissions = Permission::where('name', 'like', '%.view')->get();
        $viewerRole->permissions()->sync($viewerPermissions->pluck('id'));

        // Create default super admin user
        $superAdmin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Super Administrator',
            'password' => Hash::make('password'),
            'role_id' => $superAdminRole->id,
            'status' => 'active',
            'email_verified_at' => now()
        ]);
    }
}
