# 🔧 Hướng dẫn khắc phục lỗi "Routes not defined" trong Laravel

## 📋 Tổng quan vấn đề

### Lỗi gặp phải:
```
common-helpers.js:141 Contract Types routes not defined
common-helpers.js:141 Asset Fields routes not defined  
common-helpers.js:141 Asset Templates routes not defined
```

### Nguyên nhân:
- JavaScript modules được load trước khi routes được định nghĩa
- `@yield('page-script')` được render trước `@push('scripts')`
- Function `checkRoutes()` không tìm thấy `window.moduleRoutes`

## ✅ Giải pháp đã triển khai

### 1. Sửa Layout Scripts
**File:** `resources/views/layouts/sections/scripts.blade.php`

**Trước khi sửa:**
```blade
<!-- BEGIN: Page JS-->
@yield('page-script')
<!-- END: Page JS-->

@stack('modals')
```

**Sau khi sửa:**
```blade
<!-- BEGIN: Page JS-->
@yield('page-script')
<!-- END: Page JS-->

<!-- BEGIN: Page Scripts Stack-->
@stack('scripts')
<!-- END: Page Scripts Stack-->

@stack('modals')
```

### 2. Thứ tự loading mới:
1. ✅ Vendor scripts
2. ✅ Common helpers
3. ✅ Page scripts (JavaScript modules)
4. ✅ **Routes definition** (từ @push('scripts'))
5. ✅ Modals

## 🎯 Cách sử dụng đúng trong Blade templates

### Template chuẩn cho modules:
```blade
@extends('layouts/layoutMaster')

@section('title', 'Module Name')

<!-- Vendor Scripts -->
@section('vendor-script')
@vite(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite(['resources/js/module-name.js'])
@endsection

@section('content')
<!-- Your content here -->
@endsection

<!-- Routes Definition - MUST be after @section('page-script') -->
@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.moduleRoutes = {
  index: '{{ route("module.index") }}',
  store: '{{ route("module.store") }}',
  show: function(id) { return `/module/${id}`; },
  update: function(id) { return `/module/${id}`; },
  destroy: function(id) { return `/module/${id}`; }
};

// Pass additional data if needed
window.moduleData = @json($data);
</script>
@endpush
```

## 🔍 Kiểm tra các modules hiện tại

### ✅ Modules đã được fix:
- **Contract Types** (`resources/views/contract-types/index.blade.php`)
- **Asset Fields** (`resources/views/asset-fields/index.blade.php`)  
- **Asset Templates** (`resources/views/asset-templates/index.blade.php`)

### 🧪 Test function trong JavaScript:
```javascript
// Initialize when DOM is ready
$(document).ready(function() {
  // Check if routes are available
  if (!checkRoutes(window.moduleRoutes, 'Module Name')) {
    return; // Stop execution if routes not found
  }

  initializeDataTable();
  initializeEventHandlers();
});
```

## 🚨 Lưu ý quan trọng

### ❌ KHÔNG làm:
```blade
<!-- WRONG: Routes trong @section('page-script') -->
@section('page-script')
<script>
window.moduleRoutes = {...};
</script>
@vite(['resources/js/module-name.js'])
@endsection
```

### ✅ ĐÚNG cách:
```blade
<!-- CORRECT: Tách riêng routes và scripts -->
@section('page-script')
@vite(['resources/js/module-name.js'])
@endsection

@push('scripts')
<script>
window.moduleRoutes = {...};
</script>
@endpush
```

## 🔧 Debug và troubleshooting

### 1. Kiểm tra thứ tự loading:
```javascript
console.log('1. Page script loaded');
console.log('2. Routes available?', typeof window.moduleRoutes !== 'undefined');
```

### 2. Kiểm tra trong Browser DevTools:
- Mở **Console** tab
- Reload trang
- Kiểm tra có lỗi "routes not defined" không
- Verify `window.moduleRoutes` có tồn tại không

### 3. Common issues:
- **Routes undefined**: Kiểm tra `@push('scripts')` có đúng vị trí không
- **DataTable không load**: Kiểm tra routes và permissions
- **AJAX errors**: Kiểm tra CSRF token và route definitions

## 📝 Checklist cho modules mới

- [ ] JavaScript file trong `resources/js/`
- [ ] CSS file trong `resources/css/`
- [ ] Blade template sử dụng đúng structure
- [ ] Routes định nghĩa trong `@push('scripts')`
- [ ] JavaScript sử dụng `checkRoutes()` function
- [ ] DataTables implementation đúng chuẩn
- [ ] Test trên browser không có lỗi console

## 🎉 Kết quả sau khi fix

- ✅ Không còn lỗi "routes not defined"
- ✅ DataTables load dữ liệu thành công
- ✅ CRUD operations hoạt động bình thường
- ✅ Tất cả modules (Contract Types, Asset Fields, Asset Templates) hoạt động ổn định
