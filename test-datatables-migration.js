/**
 * Test script to verify DataTables migration from Yajra to Client-side
 * Run this in browser console on any module page
 */

function testDataTablesMigration() {
    console.log('🧪 Testing DataTables Migration from Yajra to Client-side...\n');
    
    const modules = [
        {
            name: 'Contract Types',
            tableId: 'contractTypesTable',
            routesVar: 'contractTypesRoutes',
            url: '/contract-types',
            expectedColumns: ['name', 'code', 'description', 'templates_count', 'status_badge', 'sort_order', 'action']
        },
        {
            name: 'Asset Fields',
            tableId: 'assetFieldsTable', 
            routesVar: 'assetFieldsRoutes',
            url: '/asset-fields',
            expectedColumns: ['name', 'label', 'type_formatted', 'required_badge', 'status_badge', 'templates_count', 'sort_order', 'action']
        },
        {
            name: 'Asset Templates',
            tableId: 'assetTemplatesTable',
            routesVar: 'assetTemplatesRoutes', 
            url: '/asset-templates',
            expectedColumns: ['name', 'contract_type_name', 'fields_count', 'documents_count', 'default_badge', 'status_badge', 'action']
        }
    ];
    
    let allPassed = true;
    let currentModule = null;
    
    // Detect current module
    modules.forEach(module => {
        if (document.getElementById(module.tableId)) {
            currentModule = module;
        }
    });
    
    if (!currentModule) {
        console.log('⚠️ No recognized DataTable found on this page');
        return false;
    }
    
    console.log(`📋 Testing ${currentModule.name} module:\n`);
    
    // Test 1: Check if DataTable is initialized
    console.log('1️⃣ Testing DataTable Initialization:');
    const table = document.getElementById(currentModule.tableId);
    if (!table) {
        console.error(`❌ Table ${currentModule.tableId} not found in DOM`);
        allPassed = false;
        return false;
    }
    console.log(`✅ Table ${currentModule.tableId} exists in DOM`);
    
    if (!$.fn.DataTable.isDataTable(`#${currentModule.tableId}`)) {
        console.error(`❌ DataTable ${currentModule.tableId} is not initialized`);
        allPassed = false;
        return false;
    }
    console.log(`✅ DataTable ${currentModule.tableId} is initialized`);
    
    // Test 2: Check DataTable configuration
    console.log('\n2️⃣ Testing DataTable Configuration:');
    const dtInstance = $(`#${currentModule.tableId}`).DataTable();
    const settings = dtInstance.settings()[0];
    
    // Check if it's client-side (not server-side)
    if (settings.oFeatures.bServerSide) {
        console.error('❌ DataTable is still using server-side processing');
        allPassed = false;
    } else {
        console.log('✅ DataTable is using client-side processing');
    }
    
    // Check AJAX configuration
    if (settings.ajax && settings.ajax.url) {
        console.log(`✅ AJAX URL configured: ${settings.ajax.url}`);
        
        // Check dataSrc
        if (settings.ajax.dataSrc === 'data') {
            console.log('✅ AJAX dataSrc set to "data" (correct for new format)');
        } else {
            console.log(`⚠️ AJAX dataSrc: ${settings.ajax.dataSrc} (should be "data")`);
        }
    } else {
        console.error('❌ AJAX configuration missing');
        allPassed = false;
    }
    
    // Test 3: Check data format
    console.log('\n3️⃣ Testing Data Format:');
    
    // Make test AJAX call to check response format
    if (window[currentModule.routesVar] && window[currentModule.routesVar].index) {
        $.ajax({
            url: window[currentModule.routesVar].index,
            type: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('✅ AJAX request successful');
                
                // Check response structure
                if (response && response.data && Array.isArray(response.data)) {
                    console.log(`✅ Response has correct structure: { data: [...] }`);
                    console.log(`✅ Data array contains ${response.data.length} items`);
                    
                    // Check first item structure
                    if (response.data.length > 0) {
                        const firstItem = response.data[0];
                        console.log('✅ Sample data item:', firstItem);
                        
                        // Check expected columns
                        const missingColumns = currentModule.expectedColumns.filter(col => !(col in firstItem));
                        if (missingColumns.length === 0) {
                            console.log('✅ All expected columns present in data');
                        } else {
                            console.error(`❌ Missing columns: ${missingColumns.join(', ')}`);
                            allPassed = false;
                        }
                    }
                } else {
                    console.error('❌ Response does not have correct structure');
                    console.log('Response:', response);
                    allPassed = false;
                }
            },
            error: function(xhr, status, error) {
                console.error(`❌ AJAX request failed: ${error}`);
                console.log('Response:', xhr.responseText);
                allPassed = false;
            }
        });
    }
    
    // Test 4: Check DataTable features
    console.log('\n4️⃣ Testing DataTable Features:');
    
    // Check if search works
    const searchInput = $(`#${currentModule.tableId}_filter input`);
    if (searchInput.length) {
        console.log('✅ Search input found');
    } else {
        console.error('❌ Search input not found');
        allPassed = false;
    }
    
    // Check if pagination works
    const paginationInfo = $(`#${currentModule.tableId}_info`);
    if (paginationInfo.length) {
        console.log('✅ Pagination info found');
    } else {
        console.error('❌ Pagination info not found');
        allPassed = false;
    }
    
    // Check if length menu works
    const lengthMenu = $(`#${currentModule.tableId}_length select`);
    if (lengthMenu.length) {
        console.log('✅ Length menu found');
    } else {
        console.error('❌ Length menu not found');
        allPassed = false;
    }
    
    // Test 5: Check Vietnamese language
    console.log('\n5️⃣ Testing Vietnamese Language Support:');
    const langUrl = settings.oLanguage.sUrl;
    if (langUrl && langUrl.includes('vi.json')) {
        console.log('✅ Vietnamese language file configured');
    } else {
        console.log('⚠️ Vietnamese language file not configured');
    }
    
    // Test 6: Check responsive design
    console.log('\n6️⃣ Testing Responsive Design:');
    if (settings.responsive) {
        console.log('✅ Responsive design enabled');
    } else {
        console.log('⚠️ Responsive design not enabled');
    }
    
    // Test 7: Performance check
    console.log('\n7️⃣ Testing Performance:');
    const startTime = performance.now();
    dtInstance.search('test').draw();
    dtInstance.search('').draw();
    const endTime = performance.now();
    const searchTime = endTime - startTime;
    
    if (searchTime < 100) {
        console.log(`✅ Search performance good: ${searchTime.toFixed(2)}ms`);
    } else if (searchTime < 500) {
        console.log(`⚠️ Search performance acceptable: ${searchTime.toFixed(2)}ms`);
    } else {
        console.log(`❌ Search performance poor: ${searchTime.toFixed(2)}ms`);
        allPassed = false;
    }
    
    // Final result
    console.log('\n' + '='.repeat(60));
    if (allPassed) {
        console.log('🎉 ALL TESTS PASSED! DataTables migration successful.');
        console.log('✅ Client-side DataTables working correctly');
        console.log('✅ No Yajra dependencies detected');
        console.log('✅ Performance and features working as expected');
    } else {
        console.log('❌ SOME TESTS FAILED! Check the errors above.');
        console.log('🔧 Please review the migration guide and fix issues');
    }
    console.log('='.repeat(60));
    
    return allPassed;
}

// Auto-run test if in browser
if (typeof window !== 'undefined') {
    // Wait for DOM and DataTables to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testDataTablesMigration, 2000);
        });
    } else {
        setTimeout(testDataTablesMigration, 2000);
    }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = testDataTablesMigration;
}
