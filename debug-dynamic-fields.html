<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dynamic Fields</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .debug-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.25rem;
        }
        .test-success {
            background-color: #d1edff;
            border: 1px solid #0ea5e9;
            color: #0369a1;
        }
        .test-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">🔧 Debug Dynamic Fields</h1>
        
        <!-- Test Form -->
        <div class="debug-section">
            <div class="debug-title">Test Form</div>
            <form class="field-form-container">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="testFieldType" class="form-label">Field Type</label>
                        <select id="testFieldType" name="type" class="form-select">
                            <option value="">Chọn loại field</option>
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="select">Select</option>
                            <option value="radio">Radio</option>
                            <option value="checkbox">Checkbox</option>
                            <option value="textarea">Textarea</option>
                            <option value="file">File</option>
                            <option value="date">Date</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="testFieldLabel" class="form-label">Field Label</label>
                        <input type="text" id="testFieldLabel" name="label" class="form-control" value="Test Field">
                    </div>
                </div>
                
                <!-- Options Section -->
                <div class="field-options-section mt-3" style="display: none;">
                    <h6>Tùy chọn</h6>
                    <div class="options-container">
                        <!-- Options will be added here -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary add-option-btn mt-2">
                        <i class="ri-add-line me-1"></i>
                        Thêm tùy chọn
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Test Results -->
        <div class="debug-section">
            <div class="debug-title">Test Results</div>
            <div id="testResults"></div>
            <button type="button" class="btn btn-primary" onclick="runTests()">Run Tests</button>
            <button type="button" class="btn btn-secondary" onclick="clearResults()">Clear Results</button>
        </div>
        
        <!-- Console Output -->
        <div class="debug-section">
            <div class="debug-title">Console Output</div>
            <div id="consoleOutput" class="console-output"></div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Include our dynamic fields script -->
    <script>
        // Mock showToast function
        function showToast(type, message) {
            console.log(`TOAST [${type.toUpperCase()}]: ${message}`);
            addToConsole(`TOAST [${type.toUpperCase()}]: ${message}`);
        }
        
        // Console capture
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };
        
        function addToConsole(message) {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole('ERROR: ' + args.join(' '));
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole('WARN: ' + args.join(' '));
        };
    </script>
    
    <!-- Load dynamic fields script -->
    <script src="resources/js/dynamic-fields.js"></script>
    
    <script>
        // Test functions
        function addTestResult(testName, success, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'test-success' : 'test-error'}`;
            resultDiv.innerHTML = `<strong>${testName}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function runTests() {
            clearResults();
            console.log('=== Starting Dynamic Fields Tests ===');

            // Test 1: Check if DynamicFields is available
            if (typeof window.DynamicFields !== 'undefined') {
                addTestResult('Test 1', true, 'window.DynamicFields is available');
                console.log('✓ DynamicFields object found');

                // Run debug function
                window.DynamicFields.debugFieldTypes();
            } else {
                addTestResult('Test 1', false, 'window.DynamicFields is not available');
                console.error('✗ DynamicFields object not found');
                return;
            }

            // Test 2: Test field type configs
            try {
                const configs = window.DynamicFields.fieldTypeConfigs;
                const configKeys = Object.keys(configs);
                addTestResult('Test 2', true, `Field type configs available: ${configKeys.join(', ')}`);
                console.log('✓ Field type configs test passed');
            } catch (error) {
                addTestResult('Test 2', false, 'Field type configs failed: ' + error.message);
                console.error('✗ Field type configs test failed:', error);
            }

            // Test 3: Test field type change with various types
            const testTypes = ['text', 'select', 'radio', 'checkbox', 'unknown_type'];
            testTypes.forEach((type, index) => {
                try {
                    const selectElement = document.getElementById('testFieldType');
                    selectElement.value = type;
                    window.DynamicFields.handleFieldTypeChange(selectElement);
                    addTestResult(`Test 3.${index + 1}`, true, `handleFieldTypeChange for "${type}" executed without error`);
                    console.log(`✓ Field type change test for "${type}" passed`);
                } catch (error) {
                    addTestResult(`Test 3.${index + 1}`, false, `handleFieldTypeChange for "${type}" failed: ${error.message}`);
                    console.error(`✗ Field type change test for "${type}" failed:`, error);
                }
            });

            // Test 4: Test add option
            try {
                // First set to select type to show options section
                const selectElement = document.getElementById('testFieldType');
                selectElement.value = 'select';
                window.DynamicFields.handleFieldTypeChange(selectElement);

                const addBtn = document.querySelector('.add-option-btn');
                if (addBtn) {
                    window.DynamicFields.addFieldOption(addBtn);
                    const optionItems = document.querySelectorAll('.option-item');
                    if (optionItems.length > 0) {
                        addTestResult('Test 4', true, `Add option successful - ${optionItems.length} option(s) found`);
                        console.log('✓ Add option test passed');
                    } else {
                        addTestResult('Test 4', false, 'Add option failed - no options found');
                        console.error('✗ Add option test failed - no options created');
                    }
                } else {
                    addTestResult('Test 4', false, 'Add option button not found');
                    console.error('✗ Add option button not found');
                }
            } catch (error) {
                addTestResult('Test 4', false, 'Add option failed: ' + error.message);
                console.error('✗ Add option test failed:', error);
            }

            // Test 5: Test remove option
            try {
                const removeBtn = document.querySelector('.remove-option-btn');
                if (removeBtn) {
                    const initialCount = document.querySelectorAll('.option-item').length;
                    window.DynamicFields.removeFieldOption(removeBtn);
                    const finalCount = document.querySelectorAll('.option-item').length;

                    if (finalCount < initialCount) {
                        addTestResult('Test 5', true, `Remove option successful - ${initialCount} → ${finalCount} options`);
                        console.log('✓ Remove option test passed');
                    } else {
                        addTestResult('Test 5', false, 'Remove option failed - count unchanged');
                        console.error('✗ Remove option test failed - option count unchanged');
                    }
                } else {
                    addTestResult('Test 5', false, 'Remove option button not found');
                    console.error('✗ Remove option button not found');
                }
            } catch (error) {
                addTestResult('Test 5', false, 'Remove option failed: ' + error.message);
                console.error('✗ Remove option test failed:', error);
            }

            console.log('=== Tests Completed ===');
        }
        
        // Auto-run tests when page loads
        $(document).ready(function() {
            console.log('Page loaded, waiting 1 second before running tests...');
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
