<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Field Type Error</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .error { color: #dc3545; }
        .warning { color: #fd7e14; }
        .success { color: #198754; }
        .info { color: #0dcaf0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">🔧 Debug Field Type Error</h1>
        
        <!-- Simulate Asset Field Modal -->
        <div class="debug-section">
            <h5>Simulated Asset Field Modal</h5>
            <form class="field-form-container">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="assetFieldType" class="form-label">Field Type</label>
                        <select id="assetFieldType" name="type" class="form-select">
                            <option value="">Chọn loại field</option>
                            <option value="text">Text</option>
                            <option value="number">Number</option>
                            <option value="select">Select</option>
                            <option value="textarea">Textarea</option>
                            <option value="radio">Radio</option>
                            <option value="checkbox">Checkbox</option>
                            <option value="file">File</option>
                            <option value="date">Date</option>
                        </select>
                    </div>
                </div>
                
                <!-- Options Section -->
                <div class="field-options-section mt-3" style="display: none;">
                    <h6>Tùy chọn</h6>
                    <div class="options-container">
                        <!-- Options will be added here -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary add-option-btn mt-2">
                        <i class="ri-add-line me-1"></i>
                        Thêm tùy chọn
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Test Controls -->
        <div class="debug-section">
            <h5>Test Controls</h5>
            <div class="row g-2">
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary w-100" onclick="testNormalFieldType()">Test Normal Field Type</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-warning w-100" onclick="testUndefinedFieldType()">Test Undefined Field Type</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-info w-100" onclick="testDOMReadiness()">Test DOM Readiness</button>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearConsole()">Clear Console</button>
                </div>
            </div>
            <div class="row g-2 mt-2">
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-danger w-100" onclick="testNullElement()">Test Null Element</button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-warning w-100" onclick="testMissingContainer()">Test Missing Container</button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-info w-100" onclick="simulateEditField()">Simulate Edit Field</button>
                </div>
            </div>
        </div>
        
        <!-- Console Output -->
        <div class="debug-section">
            <h5>Console Output</h5>
            <div id="consoleOutput" class="console-output"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <script>
        // Console capture
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warning' : type === 'info' ? 'info' : '';
            consoleOutput.innerHTML += `<span class="${className}">[${timestamp}] ${type.toUpperCase()}: ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        // Override console methods
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addToConsole(args.join(' '), 'info');
        };
        
        // Mock showToast function
        function showToast(type, message) {
            console.log(`TOAST [${type.toUpperCase()}]: ${message}`);
        }
    </script>
    
    <!-- Load dynamic fields script -->
    <script src="resources/js/dynamic-fields.js"></script>
    
    <script>
        // Test functions
        function testNormalFieldType() {
            console.log('=== Testing Normal Field Type ===');
            const selectElement = document.getElementById('assetFieldType');
            selectElement.value = 'textarea';
            
            if (typeof window.DynamicFields !== 'undefined') {
                window.DynamicFields.handleFieldTypeChange(selectElement);
            } else {
                console.error('DynamicFields not available');
            }
        }
        
        function testUndefinedFieldType() {
            console.log('=== Testing Undefined Field Type ===');
            const selectElement = document.getElementById('assetFieldType');
            selectElement.value = '';
            
            if (typeof window.DynamicFields !== 'undefined') {
                window.DynamicFields.handleFieldTypeChange(selectElement);
            } else {
                console.error('DynamicFields not available');
            }
        }
        
        function testNullElement() {
            console.log('=== Testing Null Element ===');
            if (typeof window.DynamicFields !== 'undefined') {
                window.DynamicFields.handleFieldTypeChange(null);
            } else {
                console.error('DynamicFields not available');
            }
        }
        
        function testMissingContainer() {
            console.log('=== Testing Missing Container ===');
            // Create a select element without proper container
            const tempSelect = document.createElement('select');
            tempSelect.value = 'text';
            
            if (typeof window.DynamicFields !== 'undefined') {
                window.DynamicFields.handleFieldTypeChange(tempSelect);
            } else {
                console.error('DynamicFields not available');
            }
        }
        
        function testDOMReadiness() {
            console.log('=== Testing DOM Readiness ===');
            if (typeof window.DynamicFields !== 'undefined' && window.DynamicFields.validateDOMReadiness) {
                window.DynamicFields.validateDOMReadiness();
            } else {
                console.error('DynamicFields.validateDOMReadiness not available');
            }
        }
        
        function simulateEditField() {
            console.log('=== Simulating Edit Field (like from database) ===');
            
            // Simulate field data from database
            const fieldData = {
                id: 1,
                name: 'dia_chi',
                label: 'Địa chỉ',
                type: 'textarea',
                options: null
            };
            
            console.log('Loaded field data:', fieldData);
            console.log('Field type from database:', fieldData.type);
            
            // Set the select value
            const selectElement = document.getElementById('assetFieldType');
            selectElement.value = fieldData.type;
            console.log('Setting field type select to:', fieldData.type);
            
            // Call the handler
            if (typeof window.DynamicFields !== 'undefined') {
                window.DynamicFields.handleFieldTypeChange(selectElement);
            } else {
                console.error('DynamicFields not available');
            }
        }
        
        // Auto-run basic test when page loads
        $(document).ready(function() {
            console.log('Page loaded, running basic tests...');
            setTimeout(() => {
                if (typeof window.DynamicFields !== 'undefined') {
                    window.DynamicFields.debugFieldTypes();
                    testDOMReadiness();
                } else {
                    console.error('DynamicFields not loaded');
                }
            }, 500);
        });
    </script>
</body>
</html>
