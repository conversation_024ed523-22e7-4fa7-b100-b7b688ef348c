<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Routes Loading Order</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Routes Loading Order</h1>
    <div id="results"></div>

    <!-- Simulate the old order (page-script before scripts stack) -->
    <h2>Old Order (Broken):</h2>
    <div id="old-order-test">
        <script>
            // Simulate page script loading first
            console.log('Page script loaded - checking routes...');
            if (typeof window.testRoutes === 'undefined') {
                console.error('❌ OLD ORDER: Routes not defined when page script loads');
                document.getElementById('old-order-test').innerHTML += '<p style="color: red;">❌ Routes not defined when page script loads</p>';
            } else {
                console.log('✅ OLD ORDER: Routes available');
                document.getElementById('old-order-test').innerHTML += '<p style="color: green;">✅ Routes available</p>';
            }
        </script>
        
        <!-- Routes defined after page script (this would be in @stack('scripts')) -->
        <script>
            window.testRoutes = {
                index: '/test',
                store: '/test',
                show: function(id) { return `/test/${id}`; }
            };
            console.log('Routes defined after page script');
        </script>
    </div>

    <!-- Simulate the new order (scripts stack before page-script) -->
    <h2>New Order (Fixed):</h2>
    <div id="new-order-test">
        <!-- Routes defined first (this would be in @stack('scripts')) -->
        <script>
            window.newTestRoutes = {
                index: '/test-new',
                store: '/test-new',
                show: function(id) { return `/test-new/${id}`; }
            };
            console.log('Routes defined first');
        </script>
        
        <!-- Page script loading after routes -->
        <script>
            console.log('Page script loaded - checking routes...');
            if (typeof window.newTestRoutes === 'undefined') {
                console.error('❌ NEW ORDER: Routes not defined when page script loads');
                document.getElementById('new-order-test').innerHTML += '<p style="color: red;">❌ Routes not defined when page script loads</p>';
            } else {
                console.log('✅ NEW ORDER: Routes available');
                document.getElementById('new-order-test').innerHTML += '<p style="color: green;">✅ Routes available when page script loads</p>';
            }
        </script>
    </div>

    <h2>Summary:</h2>
    <div id="summary">
        <p><strong>The fix:</strong> Adding <code>@stack('scripts')</code> after <code>@yield('page-script')</code> in the layout ensures that routes defined in <code>@push('scripts')</code> are available when JavaScript modules initialize.</p>
        
        <p><strong>Before fix:</strong> JavaScript files loaded before routes were defined → "routes not defined" error</p>
        
        <p><strong>After fix:</strong> Routes defined before JavaScript files load → modules can access routes properly</p>
    </div>
</body>
</html>
