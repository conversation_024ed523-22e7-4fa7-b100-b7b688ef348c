@extends('layouts/layoutMaster')

@section('title', 'Star Ratings - Extended UI')

@section('vendor-style')
@vite('resources/assets/vendor/libs/rateyo/rateyo.scss')
@endsection

@section('vendor-script')
@vite('resources/assets/vendor/libs/rateyo/rateyo.js')
@endsection

@section('page-script')
@vite('resources/assets/js/extended-ui-star-ratings.js')
@endsection

@section('content')
<div class="row">
  <!-- Basic-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Basic</h5>
      <div class="card-body">
        <div class="basic-ratings"></div>
      </div>
    </div>
  </div>
  <!-- /Basic-->

  <!-- Read Only-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Read Only</h5>
      <div class="card-body">
        <div class="read-only-ratings" data-rateyo-read-only="true"></div>
      </div>
    </div>
  </div>
  <!-- /Read Only-->

  <!-- Custom SVG-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Custom SVG</h5>
      <div class="card-body">
        <div class="custom-svg-ratings"></div>
      </div>
    </div>
  </div>
  <!-- /Custom SVG-->

  <!-- Half Star-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Half Star</h5>
      <div class="card-body">
        <div class="half-star-ratings" data-rateyo-half-star="true"></div>
      </div>
    </div>
  </div>
  <!-- /Half Star-->

  <!-- Full Star-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Full Star</h5>
      <div class="card-body">
        <div class="full-star-ratings" data-rateyo-full-star="true"></div>
      </div>
    </div>
  </div>
  <!-- /Full Star-->

  <!-- Multi Color-->
  <div class="col-md-4 col-sm-6 col-12 mb-6">
    <div class="card">
      <h5 class="card-header">Multi Color</h5>
      <div class="card-body">
        <div class="multi-color-ratings"></div>
      </div>
    </div>
  </div>
  <!-- /Multi Color-->

  <!-- Events-->
  <div class="col-xl-6 col-12 mb-xl-0 mb-6">
    <div class="card">
      <h5 class="card-header">Events</h5>
      <div class="card-body">
        <div class="row gy-3">
          <div class="col-md d-flex flex-column align-items-start mb-sm-0 mb-4">
            <small class="text-light fw-medium">onSet Event</small>
            <div class="onset-event-ratings"></div>
          </div>
          <div class="col-md d-flex flex-column align-items-start">
            <small class="text-light fw-medium">onChange Event</small>
            <div class="onChange-event-ratings mb-4"></div>
            <div class="counter-wrapper">
              <span class="fw-medium">Ratings:</span>
              <span class="counter"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- /Events-->

  <!-- Methods-->
  <div class="col-xl-6 col-12">
    <div class="card">
      <h5 class="card-header">Methods</h5>
      <div class="card-body">
        <div class="methods-ratings"></div>
        <div class="demo-inline-spacing">
          <button class="btn btn-primary btn-initialize">Initialize</button>
          <button class="btn btn-danger btn-destroy">Destroy</button>
          <button class="btn btn-success btn-get-rating">Get Ratings</button>
          <button class="btn btn-info btn-set-rating">Set Ratings to 1</button>
        </div>
      </div>
    </div>
  </div>
  <!-- /Methods-->
</div>
@endsection
