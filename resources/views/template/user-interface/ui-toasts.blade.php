@extends('layouts/layoutMaster')

@section('title', 'Toasts - UI elements')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/toastr/toastr.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss'
])
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite(['resources/assets/vendor/libs/toastr/toastr.js'])
@endsection

<!-- Page Script -->
@section('page-script')
@vite(['resources/assets/js/ui-toasts.js'])
@endsection

@section('content')
<!-- Toast with Animation -->
<div class="bs-toast toast toast-ex animate__animated my-2" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="2000">
  <div class="toast-header">
    <i class="ri-home-4-fill me-2"></i>
    <div class="me-auto fw-medium">Bootstrap</div>
    <small class="text-muted">11 mins ago</small>
    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
  </div>
  <div class="toast-body">
    Hello, world! This is a toast message.
  </div>
</div>
<!--/ Toast with Animation -->

<!-- Toast with Placements -->
<div class="bs-toast toast toast-placement-ex m-2" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="2000">
  <div class="toast-header">
    <i class="ri-home-4-fill me-2"></i>
    <div class="me-auto fw-medium">Bootstrap</div>
    <small class="text-muted">11 mins ago</small>
    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
  </div>
  <div class="toast-body">
    Hello, world! This is a toast message.
  </div>
</div>
<!-- Toast with Placements -->

<!-- Bootstrap Toasts with Animation -->
<div class="card mb-6">
  <h5 class="card-header">Bootstrap Toasts Example with Animation</h5>
  <div class="card-body">
    <div class="row gx-3 gy-2 align-items-end gap-4 gap-md-0">
      <div class="col-md-3">
        <label for="selectType" class="form-label">Type</label>
        <select id="selectType" class="form-select form-select-sm color-dropdown">
          <option value="text-primary" selected>Primary</option>
          <option value="text-secondary">Secondary</option>
          <option value="text-success">Success</option>
          <option value="text-danger">Danger</option>
          <option value="text-warning">Warning</option>
          <option value="text-info">Info</option>
          <option value="text-dark">Dark</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="selectAnimation" class="form-label">Animation</label>
        <select id="selectAnimation" class="form-select form-select-sm animation-dropdown">
          <optgroup label="Attention Seekers">
            <option value="animate__fade">fade</option>
            <option value="animate__bounce">bounce</option>
            <option value="animate__flash">flash</option>
            <option value="animate__pulse">pulse</option>
            <option value="animate__rubberBand">rubberBand</option>
            <option value="animate__shakeX">shake</option>
            <option value="animate__swing">swing</option>
            <option value="animate__tada" selected>tada</option>
            <option value="animate__wobble">wobble</option>
            <option value="animate__jello">jello</option>
            <option value="animate__heartBeat">heartBeat</option>
          </optgroup>

          <optgroup label="Bouncing Entrances">
            <option value="animate__bounceIn">bounceIn</option>
            <option value="animate__bounceInDown">bounceInDown</option>
            <option value="animate__bounceInLeft">bounceInLeft</option>
            <option value="animate__bounceInRight">bounceInRight</option>
            <option value="animate__bounceInUp">bounceInUp</option>
          </optgroup>

          <optgroup label="Fading Entrances">
            <option value="animate__fadeIn">fadeIn</option>
            <option value="animate__fadeInDown">fadeInDown</option>
            <option value="animate__fadeInDownBig">fadeInDownBig</option>
            <option value="animate__fadeInLeft">fadeInLeft</option>
            <option value="animate__fadeInLeftBig">fadeInLeftBig</option>
            <option value="animate__fadeInRight">fadeInRight</option>
            <option value="animate__fadeInRightBig">fadeInRightBig</option>
            <option value="animate__fadeInUp">fadeInUp</option>
            <option value="animate__fadeInUpBig">fadeInUpBig</option>
          </optgroup>

          <optgroup label="Flippers">
            <option value="animate__flip">flip</option>
            <option value="animate__flipInX">flipInX</option>
            <option value="animate__flipInY">flipInY</option>
          </optgroup>

          <optgroup label="Lightspeed">
            <option value="animate__lightSpeedInRight">lightSpeedIn</option>
          </optgroup>

          <optgroup label="Rotating Entrances">
            <option value="animate__rotateIn">rotateIn</option>
            <option value="animate__rotateInDownLeft">rotateInDownLeft</option>
            <option value="animate__rotateInDownRight">rotateInDownRight</option>
            <option value="animate__rotateInUpLeft">rotateInUpLeft</option>
            <option value="animate__rotateInUpRight">rotateInUpRight</option>
          </optgroup>

          <optgroup label="Sliding Entrances">
            <option value="animate__slideInUp">slideInUp</option>
            <option value="animate__slideInDown">slideInDown</option>
            <option value="animate__slideInLeft">slideInLeft</option>
            <option value="animate__slideInRight">slideInRight</option>
          </optgroup>

          <optgroup label="Zoom Entrances">
            <option value="animate__zoomIn">zoomIn</option>
            <option value="animate__zoomInDown">zoomInDown</option>
            <option value="animate__zoomInLeft">zoomInLeft</option>
            <option value="animate__zoomInRight">zoomInRight</option>
            <option value="animate__zoomInUp">zoomInUp</option>
          </optgroup>

          <optgroup label="Specials">
            <option value="animate__jackInTheBox">jackInTheBox</option>
            <option value="animate__rollIn">rollIn</option>
          </optgroup>
        </select>

      </div>
      <div class="col-md-3">
        <button id="showToastAnimation" class="btn btn-primary d-block">Show Toast</button>
      </div>
    </div>
  </div>
</div>
<!--/ Bootstrap Toasts with Animation -->

<!-- Bootstrap Toasts with Placement -->
<div class="card mb-6">
  <h5 class="card-header">Bootstrap Toasts Example With Placement</h5>
  <div class="card-body">
    <div class="row gx-3 gy-2 align-items-end gap-4 gap-md-0">
      <div class="col-md-3">
        <label for="selectTypeOpt" class="form-label">Type</label>
        <select id="selectTypeOpt" class="form-select form-select-sm color-dropdown">
          <option value="text-primary" selected>Primary</option>
          <option value="text-secondary">Secondary</option>
          <option value="text-success">Success</option>
          <option value="text-danger">Danger</option>
          <option value="text-warning">Warning</option>
          <option value="text-info">Info</option>
          <option value="text-dark">Dark</option>
        </select>
      </div>
      <div class="col-md-3">
        <label for="selectPlacement" class="form-label">Placement</label>
        <select class="form-select form-select-sm placement-dropdown" id="selectPlacement">
          <option value="top-0 start-0">Top left</option>
          <option value="top-0 start-50 translate-middle-x">Top center</option>
          <option value="top-0 end-0">Top right</option>
          <option value="top-50 start-0 translate-middle-y">Middle left</option>
          <option value="top-50 start-50 translate-middle">Middle center</option>
          <option value="top-50 end-0 translate-middle-y">Middle right</option>
          <option value="bottom-0 start-0">Bottom left</option>
          <option value="bottom-0 start-50 translate-middle-x">Bottom center</option>
          <option value="bottom-0 end-0">Bottom right</option>
        </select>
      </div>
      <div class="col-md-3">
        <button id="showToastPlacement" class="btn btn-primary d-block">Show Toast</button>
      </div>
    </div>
  </div>
</div>
<!--/ Bootstrap Toasts with Placement -->

<!-- Bootstrap Toasts Styles -->
<div class="card mb-6">
  <h5 class="card-header">Bootstrap Toasts Styles</h5>
  <div class="row g-0">
    <div class="col-md-6 p-6">
      <div class="text-light small fw-medium mb-4">Default</div>
      <div class="toast-container position-relative">

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-home-4-fill me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-bootstrap-fill text-primary me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-spam-fill text-secondary me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-checkbox-circle-fill text-success me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-error-warning-fill text-danger me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-alert-fill text-warning me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-information-fill text-info me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-contrast-fill text-dark me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6 ui-bg-overlay-container p-6">
      <div class="ui-bg-overlay bg-dark rounded-end-bottom"></div>
      <div class="text-white small fw-medium mb-4">Translucent</div>

      <div class="toast-container position-relative">
        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-home-4-fill me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-bootstrap-fill text-primary me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-spam-fill text-secondary me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-checkbox-circle-fill text-success me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-error-warning-fill text-danger me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-alert-fill text-warning me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-information-fill text-info me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>

        <div class="bs-toast toast fade show" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="toast-header">
            <i class="ri-contrast-fill text-dark me-2"></i>
            <div class="me-auto fw-medium">Bootstrap</div>
            <small class="text-muted">11 mins ago</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
          <div class="toast-body">
            Hello, world! This is a toast message.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--/ Bootstrap Toasts Styles -->

<!-- Toastr Demo -->
<div class="card">
  <h5 class="card-header">Toastr</h5>
  <div class="card-body">
    <div class="row">
      <div class="col-lg-6 col-xl-3">
        <div class="form-floating form-floating-outline mb-6">
          <input id="title" type="text" class="form-control" placeholder="Enter a title ..." />
          <label for="title">Title</label>
        </div>
        <div class="form-floating form-floating-outline mb-6">
          <textarea class="form-control" id="message" rows="3" placeholder="Enter a message ..." spellcheck="false" style="height: 81px;"></textarea>
          <label for="message">Message</label>
        </div>
        <div class="mb-6">
          <div class="form-check">
            <input id="closeButton" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="closeButton">Close Button</label>
          </div>
          <div class="form-check">
            <input id="addBehaviorOnToastClick" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="addBehaviorOnToastClick">Add behavior on toast click</label>
          </div>
          <div class="form-check">
            <input id="debugInfo" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="debugInfo">Debug</label>
          </div>
          <div class="form-check">
            <input id="progressBar" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="progressBar">Progress Bar</label>
          </div>
          <div class="form-check">
            <input id="preventDuplicates" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="preventDuplicates">Prevent Duplicates</label>
          </div>
          <div class="form-check">
            <input id="addClear" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="addClear">Add button to force clearing a toast, ignoring
              focus</label>
          </div>
          <div class="form-check">
            <input id="newestOnTop" class="form-check-input" type="checkbox" value="checked" />
            <label class="form-check-label" for="newestOnTop">Newest on top</label>
          </div>
        </div>
      </div>
      <div class="col-lg-6 col-xl-3">
        <div class="mb-4" id="toastTypeGroup">
          <label class="form-label">Toast Type</label>
          <div class="form-check">
            <input type="radio" id="successRadio" name="toastsRadio" class="form-check-input" checked value="success" />
            <label class="form-check-label" for="successRadio">Success</label>
          </div>
          <div class="form-check">
            <input type="radio" id="infoRadio" name="toastsRadio" class="form-check-input" value="info" />
            <label class="form-check-label" for="infoRadio">Info</label>
          </div>
          <div class="form-check">
            <input type="radio" id="warningRadio" name="toastsRadio" class="form-check-input" value="warning" />
            <label class="form-check-label" for="warningRadio">Warning</label>
          </div>
          <div class="form-check">
            <input type="radio" id="errorRadio" name="toastsRadio" class="form-check-input" value="error" />
            <label class="form-check-label" for="errorRadio">Error</label>
          </div>
        </div>
        <div class="mb-4" id="positionGroup">
          <label class="form-label">Position</label>
          <div class="form-check">
            <input type="radio" id="topRightRadio" name="positionsRadio" class="form-check-input" value="toast-top-right" />
            <label class="form-check-label" for="topRightRadio">Top Right</label>
          </div>
          <div class="form-check">
            <input type="radio" id="bottomRightRadio" name="positionsRadio" class="form-check-input" value="toast-bottom-right" />
            <label class="form-check-label" for="bottomRightRadio">Bottom Right</label>
          </div>
          <div class="form-check">
            <input type="radio" id="bottomLeftRadio" name="positionsRadio" class="form-check-input" value="toast-bottom-left" />
            <label class="form-check-label" for="bottomLeftRadio">Bottom Left</label>
          </div>
          <div class="form-check">
            <input type="radio" id="topLeftRadio" name="positionsRadio" class="form-check-input" value="toast-top-left" />
            <label class="form-check-label" for="topLeftRadio">Top Left</label>
          </div>
          <div class="form-check">
            <input type="radio" id="topFullWidthRadio" name="positionsRadio" class="form-check-input" value="toast-top-full-width" />
            <label class="form-check-label" for="topFullWidthRadio">Top Full Width</label>
          </div>
          <div class="form-check">
            <input type="radio" id="bottomFullWidthRadio" name="positionsRadio" class="form-check-input" value="toast-bottom-full-width" />
            <label class="form-check-label" for="bottomFullWidthRadio">Bottom Full Width</label>
          </div>
          <div class="form-check">
            <input type="radio" id="topCenterRadio" name="positionsRadio" class="form-check-input" value="toast-top-center" />
            <label class="form-check-label" for="topCenterRadio">Top Center</label>
          </div>
          <div class="form-check">
            <input type="radio" id="bottomCenterRadio" name="positionsRadio" class="form-check-input" value="toast-bottom-center" />
            <label class="form-check-label" for="bottomCenterRadio">Bottom Center</label>
          </div>
        </div>
      </div>
      <div class="col-lg-6 col-xl-3">
        <div class="mb-6 form-floating form-floating-outline">
          <input id="showEasing" type="text" class="form-control" placeholder="swing, linear" value="swing" />
          <label for="showEasing">Show Easing</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline">
          <input id="hideEasing" type="text" class="form-control" placeholder="swing, linear" value="linear" />
          <label for="hideEasing">Hide Easing</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline">
          <input id="showMethod" type="text" class="form-control" placeholder="show, fadeIn, slideDown" value="fadeIn" />
          <label for="showMethod">Show Method</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline">
          <input id="hideMethod" type="text" class="form-control" placeholder="hide, fadeOut, slideUp" value="fadeOut" />
          <label for="hideMethod">Hide Method</label>
        </div>
      </div>
      <div class="col-lg-6 col-xl-3">
        <div class="mb-6 form-floating form-floating-outline kt-form__grou">
          <input id="showDuration" type="text" class="form-control" placeholder="ms" value="300" />
          <label for="showDuration">Show Duration</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline kt-form__grou">
          <input id="hideDuration" type="text" class="form-control" placeholder="ms" value="1000" />
          <label for="hideDuration">Hide Duration</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline kt-form__grou">
          <input id="timeOut" type="text" class="form-control" placeholder="ms" value="5000" />
          <label for="timeOut">Time out</label>
        </div>
        <div class="mb-6 form-floating form-floating-outline kt-form__grou">
          <input id="extendedTimeOut" class="form-control" type="text" placeholder="ms" value="1000" />
          <label for="extendedTimeOut">Extended time out</label>
        </div>
      </div>
    </div>
    <hr />

    <div class="d-flex flex-wrap gap-2">
      <a href="javascript:;" class="btn btn-primary mb-1" id="showtoast">Show Toast</a>
      <a href="javascript:;" class="btn btn-danger mb-1" id="cleartoasts">Clear Toasts</a>
      <a href="javascript:;" class="btn btn-danger mb-1" id="clearlasttoast">Clear Last Toast</a>
    </div>
  </div>
</div>
<!--/ Toastr Demo -->
@endsection
