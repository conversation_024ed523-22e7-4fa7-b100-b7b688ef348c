@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', '<PERSON> tiết hồ sơ - ' . $document->document_number)

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite([
  'resources/css/documents-list.css'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <!-- Document Header -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <div>
          <h5 class="card-title mb-0">
            <i class="ri-file-text-line me-2"></i>
            {{ $document->title }}
          </h5>
          <small class="text-muted"><PERSON><PERSON> hồ sơ: {{ $document->document_number }}</small>
        </div>
        <div class="d-flex gap-2">
          @can('documents.edit')
            @if($document->isEditable())
              <a href="{{ route('documents.edit', $document) }}" class="btn btn-outline-warning">
                <i class="ri-edit-line me-1"></i>
                Chỉnh sửa
              </a>
            @endif
          @endcan
          
          @can('documents.export')
            <button type="button" class="btn btn-outline-success" onclick="exportDocument({{ $document->id }})">
              <i class="ri-download-line me-1"></i>
              Xuất file
            </button>
          @endcan
          
          <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>
            Quay lại
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td class="fw-medium" width="150">Loại hợp đồng:</td>
                <td>{{ $document->contractType->name ?? '-' }}</td>
              </tr>
              <tr>
                <td class="fw-medium">Template:</td>
                <td>{{ $document->assetTemplate->name ?? '-' }}</td>
              </tr>
              <tr>
                <td class="fw-medium">Trạng thái:</td>
                <td>
                  @php
                    $statusClasses = [
                        'draft' => 'bg-label-secondary',
                        'processing' => 'bg-label-warning', 
                        'completed' => 'bg-label-success',
                        'cancelled' => 'bg-label-danger'
                    ];
                    $class = $statusClasses[$document->status] ?? 'bg-label-secondary';
                  @endphp
                  <span class="badge {{ $class }}">{{ $document->formatted_status }}</span>
                </td>
              </tr>
              <tr>
                <td class="fw-medium">Người tạo:</td>
                <td>{{ $document->user->name ?? '-' }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td class="fw-medium" width="150">Ngày tạo:</td>
                <td>{{ $document->created_at->format('d/m/Y H:i') }}</td>
              </tr>
              <tr>
                <td class="fw-medium">Cập nhật:</td>
                <td>{{ $document->updated_at->format('d/m/Y H:i') }}</td>
              </tr>
              @if($document->completed_at)
              <tr>
                <td class="fw-medium">Hoàn thành:</td>
                <td>{{ $document->completed_at->format('d/m/Y H:i') }}</td>
              </tr>
              @endif
              <tr>
                <td class="fw-medium">Tổng giá trị:</td>
                <td>
                  @if($document->getTotalEstimatedValue() > 0)
                    <span class="fw-medium text-success">
                      {{ number_format($document->getTotalEstimatedValue(), 0, ',', '.') }} VND
                    </span>
                  @else
                    <span class="text-muted">Chưa định giá</span>
                  @endif
                </td>
              </tr>
            </table>
          </div>
        </div>
        
        @if($document->description)
        <div class="mt-3">
          <h6 class="fw-medium">Mô tả:</h6>
          <p class="text-muted">{{ $document->description }}</p>
        </div>
        @endif
        
        @if($document->notes)
        <div class="mt-3">
          <h6 class="fw-medium">Ghi chú:</h6>
          <p class="text-muted">{{ $document->notes }}</p>
        </div>
        @endif
      </div>
    </div>

    <!-- Document Parties -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="card-title mb-0">
          <i class="ri-group-line me-2"></i>
          Thông tin đương sự ({{ $document->parties->count() }})
        </h6>
      </div>
      <div class="card-body">
        @if($document->parties->count() > 0)
          <div class="row">
            @foreach($document->parties as $party)
            <div class="col-md-6 mb-3">
              <div class="border rounded p-3">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <h6 class="mb-0">{{ $party->full_name }}</h6>
                  <span class="badge bg-label-primary">{{ $party->formatted_party_type }}</span>
                </div>
                <div class="text-muted small">
                  <div><strong>Năm sinh:</strong> {{ $party->birth_year }} ({{ $party->age }} tuổi)</div>
                  <div><strong>{{ $party->formatted_id_type }}:</strong> {{ $party->id_number }}</div>
                  <div><strong>Địa chỉ:</strong> {{ $party->current_address }}</div>
                  @if($party->phone)
                    <div><strong>Điện thoại:</strong> {{ $party->phone }}</div>
                  @endif
                  @if($party->email)
                    <div><strong>Email:</strong> {{ $party->email }}</div>
                  @endif
                  @if($party->occupation)
                    <div><strong>Nghề nghiệp:</strong> {{ $party->occupation }}</div>
                  @endif
                </div>
              </div>
            </div>
            @endforeach
          </div>
        @else
          <div class="text-center text-muted py-4">
            <i class="ri-group-line ri-48px mb-3"></i>
            <p>Chưa có thông tin đương sự</p>
          </div>
        @endif
      </div>
    </div>

    <!-- Document Assets -->
    <div class="card">
      <div class="card-header">
        <h6 class="card-title mb-0">
          <i class="ri-building-line me-2"></i>
          Thông tin tài sản ({{ $document->assets->count() }})
        </h6>
      </div>
      <div class="card-body">
        @if($document->assets->count() > 0)
          @foreach($document->assets as $asset)
          <div class="border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-start mb-3">
              <div>
                <h6 class="mb-1">{{ $asset->asset_name }}</h6>
                @if($asset->asset_code)
                  <small class="text-muted">Mã: {{ $asset->asset_code }}</small>
                @endif
              </div>
              <div class="text-end">
                @if($asset->estimated_value)
                  <div class="fw-medium text-success">{{ $asset->formatted_estimated_value }}</div>
                @endif
              </div>
            </div>
            
            @if($asset->description)
            <div class="mb-3">
              <strong>Mô tả:</strong> {{ $asset->description }}
            </div>
            @endif

            @if($asset->field_values && count($asset->field_values) > 0)
            <div class="row">
              @foreach($asset->field_values as $fieldName => $fieldValue)
                @if($fieldValue)
                <div class="col-md-6 mb-2">
                  <div class="d-flex">
                    <span class="fw-medium me-2" style="min-width: 120px;">
                      {{ ucfirst(str_replace('_', ' ', $fieldName)) }}:
                    </span>
                    <span class="text-muted">{{ $fieldValue }}</span>
                  </div>
                </div>
                @endif
              @endforeach
            </div>
            @endif

            @if($asset->attachments && count($asset->attachments) > 0)
            <div class="mt-3">
              <strong>File đính kèm:</strong>
              <div class="d-flex flex-wrap gap-2 mt-2">
                @foreach($asset->getAttachmentUrls() as $index => $url)
                <a href="{{ $url }}" target="_blank" class="btn btn-sm btn-outline-info">
                  <i class="ri-attachment-line me-1"></i>
                  File {{ $index + 1 }}
                </a>
                @endforeach
              </div>
            </div>
            @endif

            @if($asset->notes)
            <div class="mt-3">
              <strong>Ghi chú:</strong> {{ $asset->notes }}
            </div>
            @endif
          </div>
          @endforeach
        @else
          <div class="text-center text-muted py-4">
            <i class="ri-building-line ri-48px mb-3"></i>
            <p>Chưa có thông tin tài sản</p>
          </div>
        @endif
      </div>
    </div>
  </div>
</div>

@push('scripts')
<script>
// Global function for export document
function exportDocument(documentId) {
  const link = document.createElement('a');
  link.href = `/documents/${documentId}/export`;
  link.download = `ho-so-${documentId}.docx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Show toast notification
  showToast('info', 'Đang tạo file xuất...');
}

function showToast(type, message) {
  // Simple toast implementation
  const toastId = 'toast-' + Date.now();
  const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="ri-information-line me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  `;

  // Add to toast container or create one
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  // Show toast
  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: 3000
  });
  
  toast.show();

  // Remove from DOM after hidden
  toastElement.addEventListener('hidden.bs.toast', function() {
    toastElement.remove();
  });
}
</script>
@endpush
@endsection
