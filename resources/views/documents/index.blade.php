@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', '<PERSON><PERSON> sách hồ sơ')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/flatpickr/flatpickr.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
<style>
/* Inline CSS for documents list - temporary fix */
.card-datatable {
  padding: 0;
}

.datatables-documents {
  border-collapse: separate;
  border-spacing: 0;
}

.datatables-documents thead th {
  background-color: var(--bs-gray-50);
  border-bottom: 2px solid var(--bs-gray-200);
  font-weight: 600;
  color: var(--bs-gray-800);
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.datatables-documents tbody td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--bs-gray-100);
}

.datatables-documents tbody tr:hover {
  background-color: var(--bs-gray-50);
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.bg-label-secondary {
  background-color: var(--bs-gray-100) !important;
  color: var(--bs-gray-700) !important;
}

.bg-label-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
  color: var(--bs-warning) !important;
}

.bg-label-success {
  background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  color: var(--bs-success) !important;
}

.bg-label-danger {
  background-color: rgba(var(--bs-danger-rgb), 0.1) !important;
  color: var(--bs-danger) !important;
}

.bg-label-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  color: var(--bs-primary) !important;
}

.bg-label-info {
  background-color: rgba(var(--bs-info-rgb), 0.1) !important;
  color: var(--bs-info) !important;
}
</style>
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/flatpickr/flatpickr.js'
])
@endsection

<!-- Page Script -->
@section('page-script')
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-file-text-line me-2"></i>
          Danh sách hồ sơ tài liệu
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('documents.create')
            <a href="{{ route('documents.wizard') }}" class="btn btn-primary">
              <i class="ri-add-line me-1"></i>
              Tạo hồ sơ mới
            </a>
            @endcan
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card-body border-bottom">
        <div class="row g-3">
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <select id="filterContractType" class="form-select">
                <option value="">Tất cả loại hợp đồng</option>
                @foreach($contractTypes as $contractType)
                  <option value="{{ $contractType->id }}">{{ $contractType->name }}</option>
                @endforeach
              </select>
              <label for="filterContractType">Loại hợp đồng</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <select id="filterStatus" class="form-select">
                <option value="">Tất cả trạng thái</option>
                @foreach($statusOptions as $key => $value)
                  <option value="{{ $key }}">{{ $value }}</option>
                @endforeach
              </select>
              <label for="filterStatus">Trạng thái</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="text" id="filterDateFrom" class="form-control flatpickr-date" placeholder="Từ ngày">
              <label for="filterDateFrom">Từ ngày</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-floating form-floating-outline">
              <input type="text" id="filterDateTo" class="form-control flatpickr-date" placeholder="Đến ngày">
              <label for="filterDateTo">Đến ngày</label>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="form-floating form-floating-outline">
              <input type="text" id="globalSearch" class="form-control" placeholder="Tìm kiếm theo số hồ sơ, tên đương sự, CCCD...">
              <label for="globalSearch">Tìm kiếm</label>
            </div>
          </div>
          <div class="col-md-6">
            <button type="button" class="btn btn-outline-secondary me-2" id="clearFilters">
              <i class="ri-refresh-line me-1"></i>
              Xóa bộ lọc
            </button>
            <button type="button" class="btn btn-outline-success" id="exportBtn">
              <i class="ri-download-line me-1"></i>
              Xuất Excel
            </button>
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="documentsTable" class="datatables-documents table">
          <thead>
            <tr>
              <th>Số hồ sơ</th>
              <th>Tiêu đề</th>
              <th>Loại hợp đồng</th>
              <th>Số đương sự</th>
              <th>Trạng thái</th>
              <th>Người tạo</th>
              <th>Ngày tạo</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa hồ sơ này không?</p>
        <p class="text-danger"><small>Hành động này không thể hoàn tác.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
      </div>
    </div>
  </div>
</div>

@push('scripts')
<script>
// Global variables for DataTable
let documentsTable;
let deleteDocumentId = null;

// Initialize when document ready
$(document).ready(function() {
  initializeDataTable();
  initializeFilters();
  initializeEventHandlers();
});

function initializeDataTable() {
  documentsTable = $('#documentsTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: '{{ route("documents.index") }}',
      data: function(d) {
        d.contract_type_id = $('#filterContractType').val();
        d.status = $('#filterStatus').val();
        d.date_from = $('#filterDateFrom').val();
        d.date_to = $('#filterDateTo').val();
        d.search = $('#globalSearch').val();
      }
    },
    columns: [
      { data: 'document_number', name: 'document_number' },
      { data: 'title', name: 'title' },
      { data: 'contract_type', name: 'contract_type', orderable: false },
      { data: 'parties_count', name: 'parties_count', orderable: false },
      { data: 'status_badge', name: 'status', orderable: false },
      { data: 'created_by', name: 'created_by', orderable: false },
      { data: 'created_at', name: 'created_at' },
      { data: 'action', name: 'action', orderable: false, searchable: false }
    ],
    order: [[6, 'desc']], // Sort by created_at desc
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    columnDefs: [
      {
        targets: [4], // Status column
        className: 'text-center'
      },
      {
        targets: [7], // Action column
        className: 'text-center'
      }
    ]
  });
}

function initializeFilters() {
  // Initialize flatpickr for date inputs
  $('.flatpickr-date').flatpickr({
    dateFormat: 'Y-m-d',
    allowInput: true
  });

  // Initialize select2
  $('#filterContractType, #filterStatus').select2({
    placeholder: 'Chọn...',
    allowClear: true
  });
}

function initializeEventHandlers() {
  // Filter change events
  $('#filterContractType, #filterStatus, #filterDateFrom, #filterDateTo').on('change', function() {
    documentsTable.ajax.reload();
  });

  // Global search
  $('#globalSearch').on('keyup', debounce(function() {
    documentsTable.ajax.reload();
  }, 500));

  // Clear filters
  $('#clearFilters').on('click', function() {
    $('#filterContractType, #filterStatus').val('').trigger('change');
    $('#filterDateFrom, #filterDateTo, #globalSearch').val('');
    documentsTable.ajax.reload();
  });

  // Export button
  $('#exportBtn').on('click', function() {
    // Implement export functionality
    alert('Chức năng xuất Excel sẽ được triển khai sau');
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    if (deleteDocumentId) {
      deleteDocument(deleteDocumentId);
    }
  });
}

function deleteDocument(documentId) {
  $.ajax({
    url: `/documents/${documentId}`,
    type: 'DELETE',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        $('#deleteModal').modal('hide');
        documentsTable.ajax.reload();
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      let message = 'Có lỗi xảy ra khi xóa hồ sơ';
      if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
      }
      showToast('error', message);
    }
  });
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function showToast(type, message) {
  // Implement toast notification
  // You can use your preferred toast library here
  alert(message);
}

// Global function for delete button in DataTable
function deleteDocument(documentId) {
  deleteDocumentId = documentId;
  $('#deleteModal').modal('show');
}
</script>
@endpush
@endsection
