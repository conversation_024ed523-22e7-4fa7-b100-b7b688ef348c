@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', 'Quản lý field động')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite(['resources/css/asset-fields.css'])
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

<!-- Page Script -->
@section('page-script')
@vite([
  'resources/js/common-helpers.js',
  'resources/js/dynamic-fields.js',
  'resources/js/asset-fields.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-input-field me-2"></i>
          Quản lý field động
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('asset-fields.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assetFieldModal">
              <i class="ri-add-line me-1"></i>
              Thêm field mới
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="assetFieldsTable" class="datatables-asset-fields table">
          <thead>
            <tr>
              <th>Tên field</th>
              <th>Label</th>
              <th>Loại</th>
              <th>Bắt buộc</th>
              <th>Trạng thái</th>
              <th>Số template</th>
              <th>Thứ tự</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Asset Field Modal -->
<div class="modal fade" id="assetFieldModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="assetFieldModalTitle">Thêm field mới</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="assetFieldForm" class="field-form-container">
        <div class="modal-body">
          <input type="hidden" id="assetFieldId" name="id">

          <div class="row g-3">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="assetFieldName" name="name" class="form-control" required>
                <label for="assetFieldName">Tên field (name) *</label>
                <small class="form-text text-muted">Chỉ sử dụng chữ thường, số và dấu gạch dưới</small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="assetFieldLabel" name="label" class="form-control" required>
                <label for="assetFieldLabel">Label hiển thị *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="assetFieldType" name="type" class="form-select" required>
                  <option value="">Chọn loại field</option>
                  @foreach($fieldTypes as $key => $value)
                    <option value="{{ $key }}">{{ $value }}</option>
                  @endforeach
                </select>
                <label for="assetFieldType">Loại field *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="assetFieldSortOrder" name="sort_order" class="form-control" min="0" value="0">
                <label for="assetFieldSortOrder">Thứ tự sắp xếp</label>
              </div>
            </div>
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <input type="text" id="assetFieldPlaceholder" name="placeholder" class="form-control">
                <label for="assetFieldPlaceholder">Placeholder</label>
              </div>
            </div>
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <textarea id="assetFieldHelpText" name="help_text" class="form-control" rows="2"></textarea>
                <label for="assetFieldHelpText">Text hướng dẫn</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input type="checkbox" class="form-check-input" id="assetFieldIsRequired" name="is_required">
                <label class="form-check-label" for="assetFieldIsRequired">Bắt buộc</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch">
                <input type="checkbox" class="form-check-input" id="assetFieldIsActive" name="is_active" checked>
                <label class="form-check-label" for="assetFieldIsActive">Hoạt động</label>
              </div>
            </div>
          </div>

          <!-- Options section for select, radio, checkbox -->
          <div class="field-options-section mt-4" style="display: none;">
            <h6>Tùy chọn</h6>
            <div class="options-container">
              <!-- Options will be added here -->
            </div>
            <button type="button" class="btn btn-sm btn-outline-primary add-option-btn">
              <i class="ri-add-line me-1"></i>
              Thêm tùy chọn
            </button>
          </div>

          <!-- Validation rules -->
          <div class="mt-4">
            <div class="form-floating form-floating-outline">
              <input type="text" id="assetFieldValidationRules" name="validation_rules" class="form-control">
              <label for="assetFieldValidationRules">Validation rules</label>
              <small class="form-text text-muted">Ví dụ: required|min:3|max:255 (phân cách bằng dấu |)</small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa field này không?</p>
        <p class="text-danger"><small>Hành động này không thể hoàn tác.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
      </div>
    </div>
  </div>
</div>

@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.assetFieldsRoutes = {
  index: '{{ route("asset-fields.index") }}',
  store: '{{ route("asset-fields.store") }}',
  show: function(id) { return `/asset-fields/${id}`; },
  update: function(id) { return `/asset-fields/${id}`; },
  destroy: function(id) { return `/asset-fields/${id}`; }
};

// Pass field types to JavaScript
window.fieldTypes = @json($fieldTypes);
</script>
@endpush
@endsection
