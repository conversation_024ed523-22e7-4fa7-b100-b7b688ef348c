@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

$flatpickr-content-padding-x: 0.5rem !default;
$flatpickr-content-padding-y: 0.25rem !default;
$flatpickr-cell-size: 2.25rem !default;
$flatpickr-animation-duration: 400ms !default;
$flatpickr-time-picker-height: 40px !default;
$flatpickr-week-height: 2.25rem !default;
$flatpickr-month-height: 2.5rem !default;
$flatpickr-year-height: 1.2rem !default;
$flatpickr-width: ($flatpickr-cell-size * 7)+ ($flatpickr-content-padding-x * 2) !default;

@mixin keyframes($name) {
  @-webkit-keyframes #{$name} {
    @content;
  }

  @-moz-keyframes #{$name} {
    @content;
  }

  @keyframes #{$name} {
    @content;
  }
}

.flatpickr-calendar {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0;
  padding-bottom: 4px;
  max-height: 0;
  border: 0;
  background: transparent;
  text-align: center;
  opacity: 0;
  animation: none;
  outline: 0;
  touch-action: manipulation;
  line-height: light.$line-height-base;
  font-size: light.$font-size-base;
  @include light.border-radius(light.$border-radius-xl !important);

  &.open,
  &.inline {
    visibility: visible;
    overflow: visible;
    max-height: 640px;
    opacity: 1;
  }

  &.open {
    display: inline-block;
  }

  &.animate.open {
    animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
  }

  &:not(.inline):not(.open) {
    display: none !important;
  }

  &.inline {
    position: relative;
    top: 2px;
    display: block;
  }

  &.static {
    position: absolute;
    top: calc(100% + 2px);
  }

  &.static.open {
    z-index: 999;
    display: block;
  }

  &.hasWeeks {
    width: auto;
  }

  @include app-ltr {
    &.hasWeeks .flatpickr-days {
      border-bottom-left-radius: 0 !important;
    }
  }

  @include app-rtl {
    &.hasWeeks .flatpickr-days {
      border-bottom-right-radius: 0 !important;
    }
  }

  &.hasTime {
    padding-bottom: 0;
    .flatpickr-time {
      height: $flatpickr-time-picker-height;
    }
  }

  &.noCalendar.hasTime {
    .flatpickr-time {
      height: auto;
    }
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.flatpickr-wrapper {
  position: relative;
  display: inline-block;
}

.flatpickr-month {
  position: relative;
  overflow: hidden;
  height: $flatpickr-month-height + 0.5;
  text-align: center;
  line-height: 1;
  user-select: none;
  margin-bottom: 0.25rem;
}

.flatpickr-prev-month,
.flatpickr-next-month {
  position: absolute;
  top: 0;
  z-index: 3;
  padding: 0.55rem 0.65rem 0;
  height: $flatpickr-month-height;
  text-decoration: none;
  line-height: $flatpickr-month-height;
  cursor: pointer;

  svg {
    vertical-align: middle;
  }
}

.flatpickr-prev-month i,
.flatpickr-next-month i {
  position: relative;
}

.flatpickr-prev-month {
  &.flatpickr-prev-month {
    left: 0;
  }

  @include app-rtl {
    right: 0;
    left: auto;
    transform: scaleX(-1);
  }
}

.flatpickr-next-month {
  &.flatpickr-prev-month {
    right: 0;
    left: 0;
  }

  &.flatpickr-next-month {
    right: 0;
  }

  @include app-rtl {
    right: auto;
    left: 0;
    transform: scaleX(-1);
  }
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
  width: 12px;
  height: 16px;
}

.flatpickr-prev-month svg path,
.flatpickr-next-month svg path {
  transition: fill 0.1s;
  fill: inherit;
}

.numInputWrapper {
  position: relative;
  height: auto;

  input,
  span {
    display: inline-block;
  }

  input {
    width: 100%;
  }

  span {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 14px;
    height: 50%;
    line-height: 1;
    opacity: 0;
    cursor: pointer;

    @include app-rtl {
      right: auto;
      left: 0;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    &:active {
      background: rgba(0, 0, 0, 0.2);
    }

    &:after {
      content: '';
      display: block;
      width: 0;
      height: 0;
    }

    &.arrowUp {
      top: 0;
    }

    &.arrowUp:after {
      border-right: 4px solid transparent;
      border-bottom: 4px solid rgba(72, 72, 72, 0.6);
      border-left: 4px solid transparent;
    }

    &.arrowDown {
      top: 40%;
    }

    &.arrowDown:after {
      border-top: 4px solid rgba(72, 72, 72, 0.6);
      border-right: 4px solid transparent;
      border-left: 4px solid transparent;
    }

    svg {
      width: inherit;
      height: auto;
    }

    svg path {
      fill: rgba(255, 255, 255, 0.5);
    }
  }

  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  &:hover span {
    opacity: 1;
  }
}

.flatpickr-current-month {
  position: absolute;
  left: 12.5%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  width: 75%;
  height: $flatpickr-month-height + 0.5;
  text-align: center;
  line-height: 1;
  padding: 0.75rem 0 0 0;
  transform: translate3d(0px, 0px, 0px);
  @include app-rtl {
    left: 8.5%;
  }

  .flatpickr-monthDropdown-months,
  input.cur-year {
    padding: 0 0 0 0.5ch;
    outline: none;
    vertical-align: middle !important;
    font-weight: 400;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    color: inherit;
    display: inline-block;
    box-sizing: border-box;
    background: transparent;
    border: 0;
    border-radius: 0;
  }

  .numInputWrapper {
    display: inline-block;
    width: 6ch;
  }

  .flatpickr-monthDropdown-months {
    appearance: menulist;
    cursor: pointer;
    height: $flatpickr-month-height - 0.25rem;
    // margin: -1px 0 0 0;
    position: relative;
    width: auto;
    font-size: light.$font-size-base;
  }

  input.cur-year {
    margin: 0;
    height: $flatpickr-year-height;
    cursor: default;

    @include app-rtl {
      padding-right: 0.5ch;
      padding-left: 0;
    }

    &:focus {
      outline: 0;
    }

    &[disabled],
    &[disabled]:hover {
      background: transparent;
      pointer-events: none;
    }

    &[disabled] {
      opacity: 0.5;
    }
  }
}

.flatpickr-weekdaycontainer {
  display: flex;
  width: 100%;
  padding: $flatpickr-content-padding-y $flatpickr-content-padding-x;
}

.flatpickr-weekdays {
  display: flex;
  overflow: hidden;
  align-items: center;
  max-width: 17.5rem;
  width: 100%;
  height: $flatpickr-week-height;
  text-align: center;
  margin-block: 0.375rem;
}

span.flatpickr-weekday {
  display: block;
  flex: 1;
  margin: 0;
  text-align: center;
  line-height: 1;
  cursor: default;
}

.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0 0;
}

.flatpickr-days {
  position: relative;
  display: flex;
  overflow: hidden;
  width: auto !important;

  &:focus {
    outline: 0;
  }

  .flatpickr-calendar.hasTime & {
    border-bottom: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}

.dayContainer {
  display: inline-block;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  box-sizing: border-box;
  padding: 0;
  min-width: $flatpickr-cell-size * 7;
  max-width: $flatpickr-cell-size * 7;
  width: $flatpickr-cell-size * 7;
  outline: 0;
  opacity: 1;
  transform: translate3d(0px, 0px, 0px);
}

.flatpickr-day {
  position: relative;
  display: inline-block;
  flex-basis: 14.2857143%;
  justify-content: center;
  box-sizing: border-box;
  margin: 0;
  max-width: $flatpickr-cell-size;
  width: 15.2857143%;
  height: $flatpickr-cell-size;
  border: 1px solid transparent;
  background: none;
  text-align: center;
  font-size: light.$font-size-base;
  font-weight: 400;
  line-height: calc(#{$flatpickr-cell-size} - 2px);
  cursor: pointer;

  &.inRange,
  &.prevMonthDay.inRange,
  &.nextMonthDay.inRange,
  &.today.inRange,
  &.prevMonthDay.today.inRange,
  &.nextMonthDay.today.inRange,
  &:hover,
  &.prevMonthDay:hover,
  &.nextMonthDay:hover,
  &:focus,
  &.prevMonthDay:focus,
  &.nextMonthDay:focus {
    outline: 0;
    cursor: pointer;
  }

  &.inRange:not(.startRange):not(.endRange) {
    border-radius: 0 !important;
  }

  &.disabled,
  &.flatpickr-disabled,
  &.flatpickr-disabled.today,
  &.disabled:hover,
  &.flatpickr-disabled:hover,
  &.flatpickr-disabled.today:hover {
    border-color: transparent;
    background: transparent !important;
    cursor: default;
    pointer-events: none;
  }

  &.prevMonthDay,
  &.nextMonthDay {
    border-color: transparent;
    background: transparent;
    cursor: default;
  }

  &.notAllowed,
  &.notAllowed.prevMonthDay,
  &.notAllowed.nextMonthDay {
    border-color: transparent;
    background: transparent;
    cursor: default;
  }

  &.week.selected {
    border-radius: 0;
  }

  @include app-ltr {
    &.selected.startRange,
    &.startRange.startRange,
    &.endRange.startRange {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &.selected.endRange,
    &.startRange.endRange,
    &.endRange.endRange {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  @include app-rtl {
    &.selected.startRange,
    &.startRange.startRange,
    &.endRange.startRange {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &.selected.endRange,
    &.startRange.endRange,
    &.endRange.endRange {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}

.flatpickr-weekwrapper {
  display: inline-block;
  float: left;

  .flatpickr-weeks {
    padding: $flatpickr-content-padding-y + 0.375rem 0;
    background-clip: padding-box !important;

    @include app-ltr {
      .flatpickr-weeks {
        border-bottom-right-radius: 0 !important;
      }
    }

    @include app-rtl {
      .flatpickr-weeks {
        border-bottom-left-radius: 0 !important;
      }
    }
    .flatpickr-day {
      font-size: light.$font-size-sm;
    }
  }

  .flatpickr-calendar.hasTime .flatpickr-weeks {
    border-bottom: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }

  .flatpickr-weekday {
    float: none;
    width: 100%;
    line-height: $flatpickr-week-height;
    position: relative;
    top: 0.45rem;
    margin-bottom: 0.375rem;
  }

  span.flatpickr-day {
    display: block;
    max-width: none;
    width: $flatpickr-cell-size;
    background: none !important;
  }
}

.flatpickr-calendar.hasTime .flatpickr-weeks {
  border-bottom: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.flatpickr-innerContainer {
  display: block;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  &:has(.flatpickr-weeks) {
    .flatpickr-weeks {
      @include app-ltr {
        padding-left: 0.445rem;
      }
      @include app-rtl {
        padding-left: 0.445rem;
      }
    }
    .flatpickr-weekdaycontainer {
      @include app-rtl {
        padding-left: $flatpickr-content-padding-x * 1.25;
      }
    }
    .flatpickr-weekwrapper .flatpickr-weekday {
      padding-left: 0.445rem;
    }
    .flatpickr-weekwrapper {
      @include app-rtl {
        padding-right: 0.5rem;
      }
    }
  }
}

.flatpickr-rContainer {
  display: inline-block;
  box-sizing: border-box;
  padding: 0;
}

.flatpickr-time {
  display: block;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  max-height: $flatpickr-time-picker-height;
  height: 0;
  outline: 0;
  background-clip: padding-box !important;
  text-align: center;
  line-height: $flatpickr-time-picker-height;

  &:after {
    content: '';
    display: table;
    clear: both;
  }

  .numInputWrapper {
    float: left;
    flex: 1;
    width: 40%;
    height: $flatpickr-time-picker-height;
  }

  &.hasSeconds .numInputWrapper {
    width: 26%;
  }

  &.time24hr .numInputWrapper {
    width: 49%;
  }

  input {
    position: relative;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    height: inherit;
    border: 0;
    border-radius: 0;
    background: transparent;
    box-shadow: none;
    text-align: center;
    line-height: inherit;
    cursor: pointer;
    font-size: light.$font-size-base;

    &.flatpickr-hour,
    &.flatpickr-minute,
    &.flatpickr-second {
      font-weight: light.$font-weight-normal;
    }

    &:focus {
      outline: 0;
      border: 0;
    }
  }

  .flatpickr-time-separator,
  .flatpickr-am-pm {
    display: inline-block;
    float: left;
    align-self: center;
    width: 2%;
    height: inherit;
    line-height: inherit;
    user-select: none;
  }

  .flatpickr-am-pm {
    width: 18%;
    outline: 0;
    text-align: center;
    font-weight: normal;
    cursor: pointer;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  .flatpickr-calendar.noCalendar & {
    box-shadow: none !important;
  }

  .flatpickr-calendar:not(.noCalendar) & {
    border-top: 0;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
}

.flatpickr-input[readonly] {
  cursor: pointer;
}

// Animations
//

@include keyframes(fpFadeInDown) {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
// Light layout
@if $enable-light-style {
  .light-style {
    // Dimensions
    .flatpickr-calendar,
    .flatpickr-days {
      width: calc(#{$flatpickr-width} + calc(#{light.$dropdown-border-width} * 2px)) !important;
    }

    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks {
        width: calc(
          #{$flatpickr-width + $flatpickr-cell-size} + calc(#{light.$dropdown-border-width} * 3px) + 0.35rem
        ) !important;
      }
    }
    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks {
        width: calc(
          #{$flatpickr-width + $flatpickr-cell-size} + calc(#{light.$dropdown-border-width} * 3px) + 1rem
        ) !important;
      }
    }

    .flatpickr-calendar.open {
      z-index: light.$zindex-popover;
    }

    .flatpickr-prev-month,
    .flatpickr-next-month {
      svg {
        fill: light.rgba-to-hex(light.$body-color);
        stroke: light.rgba-to-hex(light.$body-color);
      }
    }
    //!  Flatpickr provide default input as readonly, applying default input style to readonly
    .flatpickr-input[readonly],
    .flatpickr-input ~ .form-control[readonly] {
      background: #{light.$input-bg};
    }

    .flatpickr-days {
      background: #{light.$dropdown-bg};
      padding: $flatpickr-content-padding-y $flatpickr-content-padding-x $flatpickr-content-padding-x;
      border: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      border-top: 0;
      background-clip: padding-box;
      @include light.border-bottom-radius(light.$border-radius-xl);
    }

    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-left: 0;
        padding-left: calc(#{$flatpickr-content-padding-x} + #{light.$dropdown-border-width}px);
        box-shadow: light.$dropdown-border-width 0 0 opacify(light.$dropdown-border-color, 0.05) inset;
      }
    }

    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-right: 0;
        padding-right: calc(#{$flatpickr-content-padding-x} + #{light.$dropdown-border-width}px);
        box-shadow: -(light.$dropdown-border-width) 0 0 opacify(light.$dropdown-border-color, 0.05) inset;
      }
    }

    .flatpickr-calendar {
      box-shadow: light.$card-box-shadow;
      background-color: light.$card-bg;

      &.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time {
        display: none !important;
      }

      &.hasTime .flatpickr-time {
        box-shadow: 0 1px 0 light.$border-color inset;
      }
    }
    .flatpickr-monthDropdown-months {
      color: light.$headings-color;
    }
    .flatpickr-current-month {
      // ! Fix: To remove : All the datepickrs with a select for months > when we click on the select, month's select and year's input goes up a little bit

      // padding: (
      //     $flatpickr-month-height - (light.$font-size-base * light.strip-unit(light.$big-font-size) / 100) - 0.45rem
      //   )
      //   0
      //   0;
      font-size: light.$big-font-size;
      color: light.$headings-color;
      .cur-month,
      .cur-year {
        font-size: light.$font-size-base;
        font-weight: 400;
        color: light.$headings-color;
      }
    }

    .flatpickr-month,
    span.flatpickr-weekday,
    .flatpickr-weekdays {
      background: light.$dropdown-bg;
    }
    .flatpickr-month {
      @include light.border-top-radius(light.$border-radius-xl);
      // ! FIX: OS Windows and Linux Browsers DD Option color
      option.flatpickr-monthDropdown-month {
        color: light.$body-color;
        background: #{light.$input-bg};
      }
    }

    span.flatpickr-weekday {
      color: light.$headings-color;
      font-size: light.$font-size-sm;
    }

    .flatpickr-day {
      color: light.$headings-color;
      @include light.border-radius(light.$border-radius-pill);

      &:hover,
      &:focus,
      &.prevMonthDay:hover,
      &.nextMonthDay:hover,
      &.today:hover,
      &.prevMonthDay:focus,
      &.nextMonthDay:focus,
      &.today:focus {
        color: light.$body-color;
        background: light.rgba-to-hex(rgba(light.$black, 0.06), light.$rgba-to-hex-bg);
        &:not(.today) {
          border-color: transparent;
        }
      }

      &.selected,
      &.selected.inRange,
      &.selected:focus,
      &.selected:hover,
      &.selected.nextMonthDay,
      &.selected.prevMonthDay,
      &.week.selected {
        box-shadow: light.$box-shadow-xs;
      }

      &.prevMonthDay,
      &.nextMonthDay,
      &.flatpickr-disabled {
        color: light.$text-muted !important;

        &.today {
          border: none;
        }
      }

      &.disabled {
        color: light.$text-muted !important;
      }

      &.selected.startRange.endRange {
        border-radius: light.$border-radius-pill !important;
      }
    }

    .flatpickr-weeks {
      border-bottom: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      border-left: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      background: light.$card-bg;
      @include light.border-bottom-radius(light.$border-radius-xl);
      border-bottom-right-radius: 0;
      .flatpickr-day {
        color: light.$headings-color;
      }
    }

    @include app-rtl-style {
      .flatpickr-weeks {
        border-right: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
        border-left: 0;
        @include light.border-bottom-radius(light.$border-radius-xl);
        border-bottom-left-radius: 0;
      }
    }

    .flatpickr-time {
      border: light.$dropdown-border-width solid opacify(light.$dropdown-border-color, 0.05);
      background: #{light.$dropdown-bg};
      @include light.border-radius(light.$border-radius-xl);

      input {
        color: light.$body-color;
      }

      .numInputWrapper span {
        &.arrowUp:after {
          border-bottom-color: light.$text-muted;
        }

        &.arrowDown:after {
          border-top-color: light.$text-muted;
        }
      }

      .flatpickr-am-pm {
        color: light.$body-color;
      }

      .flatpickr-time-separator {
        color: light.$body-color;
      }
    }
  }
}

// Dark layout
@if $enable-dark-style {
  .dark-style {
    .flatpickr-calendar,
    .flatpickr-days {
      width: calc(#{$flatpickr-width} + calc(#{dark.$dropdown-border-width} * 2px)) !important;
    }
    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks {
        width: calc(
          #{$flatpickr-width + $flatpickr-cell-size} + calc(#{dark.$dropdown-border-width} * 3px) + 0.355rem
        ) !important;
      }
    }
    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks {
        width: calc(
          #{$flatpickr-width + $flatpickr-cell-size} + calc(#{dark.$dropdown-border-width} * 3px) + 1rem
        ) !important;
      }
    }
    .flatpickr-calendar.open {
      z-index: dark.$zindex-popover;
    }
    .flatpickr-prev-month,
    .flatpickr-next-month {
      svg {
        fill: dark.$headings-color;
        stroke: dark.$headings-color;
      }
    }
    //!  Flatpickr provide default input as readonly, applying default input style to readonly
    .flatpickr-input:not(.is-invalid):not(.is-valid) ~ .form-control:disabled,
    .flatpickr-input:not(.is-invalid):not(.is-valid)[readonly],
    .flatpickr-input:not(.is-invalid):not(.is-valid) ~ .form-control[readonly] {
      background-color: #{dark.$input-bg};
    }

    .flatpickr-days {
      border: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      border-top: 0;
      padding: $flatpickr-content-padding-y $flatpickr-content-padding-x;
      padding-bottom: $flatpickr-content-padding-x;
      background: #{dark.$dropdown-bg};
      background-clip: padding-box;
      @include dark.border-bottom-radius(dark.$border-radius-xl);
    }

    @include app-ltr-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-left: 0;
        padding-left: calc(#{$flatpickr-content-padding-x} + #{dark.$dropdown-border-width}px);
        box-shadow: dark.$dropdown-border-width 0 0 opacify(dark.$dropdown-border-color, 0.05) inset;
      }
    }

    @include app-rtl-style {
      .flatpickr-calendar.hasWeeks .flatpickr-days {
        border-right: 0;
        padding-right: calc(#{$flatpickr-content-padding-x} + #{dark.$dropdown-border-width}px);
        box-shadow: -(dark.$dropdown-border-width) 0 0 opacify(dark.$dropdown-border-color, 0.05) inset;
      }
    }
    .flatpickr-calendar {
      box-shadow: dark.$card-box-shadow;
      background-color: dark.$card-bg;
      @include dark.border-radius(dark.$border-radius-xl);

      &.hasTime:not(.noCalendar):not(.hasTime) .flatpickr-time {
        display: none !important;
      }

      &.hasTime .flatpickr-time {
        box-shadow: 0 1px 0 dark.$border-color inset;
      }
    }

    .flatpickr-month,
    span.flatpickr-weekday,
    .flatpickr-weekdays {
      background: dark.$dropdown-bg;
    }

    .flatpickr-month {
      @include dark.border-top-radius(dark.$border-radius-xl);
      // ! FIX: OS Windows and Linux Browsers DD Option color
      option.flatpickr-monthDropdown-month {
        color: dark.$body-color;
        background: #{dark.$card-bg};
      }
    }

    .flatpickr-monthDropdown-months {
      color: dark.$headings-color;
    }
    .flatpickr-current-month {
      // ! Fix: To remove : All the datepickrs with a select for months > when we click on the select, month's select and year's input goes up a little bit
      // padding: ($flatpickr-month-height - (dark.$font-size-base * dark.strip-unit(dark.$big-font-size) / 100) - 0.45rem)
      //   0
      //   0;
      font-size: dark.$big-font-size;
      color: dark.$headings-color;
      .cur-month,
      .cur-year {
        font-size: dark.$font-size-base;
        font-weight: 400;
        color: dark.$headings-color;
      }
    }

    span.flatpickr-weekday {
      color: dark.$headings-color;
      font-size: dark.$font-size-sm;
    }

    .flatpickr-day {
      color: dark.$headings-color;
      @include dark.border-radius(dark.$border-radius-pill);

      &:hover,
      &:focus,
      &.nextMonthDay:hover,
      &.prevMonthDay:hover,
      &.today:hover,
      &.nextMonthDay:focus,
      &.prevMonthDay:focus,
      &.today:focus {
        border-color: transparent;
        background: dark.rgba-to-hex(rgba(dark.$base, 0.06), dark.$rgba-to-hex-bg);
        color: dark.$body-color;
      }

      &.selected,
      &.selected.inRange,
      &.selected:focus,
      &.selected:hover,
      &.selected.nextMonthDay,
      &.selected.prevMonthDay,
      &.week.selected {
        box-shadow: dark.$box-shadow-xs;
      }

      &.nextMonthDay,
      &.prevMonthDay,
      &.flatpickr-disabled {
        color: dark.$text-muted !important;

        &.today {
          border: 0;
        }
      }

      &.selected.startRange.endRange {
        border-radius: dark.$border-radius-pill !important;
      }

      &.disabled {
        color: darken(dark.$text-muted, 30) !important;
      }
    }

    .flatpickr-weeks {
      border-bottom: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      border-left: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      background: dark.$card-bg;

      @include dark.border-bottom-radius(dark.$border-radius);
      border-bottom-right-radius: 0;
      .flatpickr-day {
        color: dark.$headings-color;
      }
    }

    @include app-rtl-style {
      .flatpickr-weeks {
        border-right: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
        border-left: 0;
      }
    }

    .flatpickr-time {
      border: dark.$dropdown-border-width solid opacify(dark.$dropdown-border-color, 0.05);
      background: #{dark.$dropdown-bg};
      @include light.border-radius(dark.$border-radius-xl);

      input {
        color: dark.$body-color;
      }

      .numInputWrapper span {
        &.arrowUp:after {
          border-bottom-color: dark.$text-muted;
        }

        &.arrowDown:after {
          border-top-color: dark.$text-muted;
        }
      }

      .flatpickr-am-pm {
        color: dark.$body-color;
      }

      .flatpickr-time-separator {
        color: dark.$body-color;
      }
    }
  }
}
