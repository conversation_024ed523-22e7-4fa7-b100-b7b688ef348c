@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

$datepicker-arrow-size: 0.5rem !default;
$datepicker-item-width: 2.25rem !default;
$datepicker-item-height: 2.25rem !default;
$white: #fff;

.datepicker {
  direction: ltr;
  //! FIX: We can't use dropdown animation here
  animation: none !important;
  &.dropdown-menu {
    padding: 0;
    margin: 0;
  }
  .datepicker-days {
    margin: 0.875rem 0.875rem 0.875rem;
  }

  .next,
  .prev {
    color: transparent !important;
    position: relative;
  }

  .next::after,
  .prev::after {
    content: '';
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    height: $datepicker-arrow-size;
    width: $datepicker-arrow-size;
    border-radius: 0;
    border-style: solid;
    transform: rotate(-45deg);
    transform-origin: left;
  }

  .next::after {
    margin-left: -$datepicker-arrow-size * 0.5;
    border-width: 0 2px 2px 0;

    @include app-rtl {
      transform: rotate(-45deg);
      border-width: 1px 0 0 1px;
      margin-left: 0;
    }
  }

  .prev::after {
    border-width: 2px 0 0 2px;

    @include app-rtl {
      transform: rotate(-45deg);
      border-width: 0 1px 1px 0;
      margin-left: -$datepicker-arrow-size * 0.5;
    }
  }

  &.datepicker-rtl {
    direction: rtl;

    table tr td span {
      float: right;
    }
  }

  @include app-rtl {
    direction: rtl;
  }
}

.datepicker table {
  user-select: none;
  margin: 0;
  overflow: hidden;
  border-radius: light.$border-radius-xl;
  tbody {
    //! FIX: padding or margin top will not work in table
    &:before {
      content: '@';
      display: block;
      line-height: 6px;
      text-indent: -99999px;
    }
  }
}

.datepicker table tr td,
.datepicker table tr th {
  font-weight: 400;
  text-align: center;
  border: none;

  &.dow {
    font-size: light.$font-size-sm;
    font-weight: light.$font-weight-normal;
  }
}
.datepicker table tr {
  &:not(:last-child) th {
    width: $datepicker-item-width;
    height: $datepicker-item-height;
  }
  &:last-child th {
    width: $datepicker-item-width;
    height: $datepicker-item-height + 0.5rem;
  }
}
.datepicker table tr td {
  border-radius: light.$border-radius-pill;
  width: $datepicker-item-width;
  height: $datepicker-item-height;
  &.day:hover,
  &.focused {
    cursor: pointer;
  }

  &.disabled,
  &.disabled:hover {
    cursor: default;
    background: none;
  }

  &.range {
    border-radius: 0 !important;
    &.today {
      box-shadow: none !important;
    }
  }

  // span.month,
  // span.year {
  //   margin: 0 0.5rem;
  // }

  &.range-start:not(.range-end) {
    @include app-ltr {
      border-bottom-right-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }

    @include app-rtl {
      border-bottom-left-radius: 0 !important;
      border-top-left-radius: 0 !important;
    }
  }

  &.range-end:not(.range-start) {
    @include app-ltr {
      border-bottom-left-radius: 0 !important;
      border-top-left-radius: 0 !important;
    }

    @include app-rtl {
      border-bottom-right-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }
  }

  &.selected,
  &.selected:hover,
  &.selected.highlighted {
    color: $white;
  }
}

.datepicker table tr td span {
  display: block;
  float: left;
  width: 3.625rem;
  height: 2rem;
  line-height: 2rem;
  cursor: pointer;

  &.disabled,
  &.disabled:hover {
    background: none;
    cursor: default;
  }

  @include app-rtl {
    float: right;
  }
}
.datepicker .datepicker-switch,
.datepicker .prev,
.datepicker .next,
.datepicker tfoot tr th {
  cursor: pointer;
}

// styling for calendar-week cells
.datepicker .cw {
  font-weight: light.$font-weight-normal;
}

.datepicker-months table,
.datepicker-years table,
.datepicker-decades table,
.datepicker-centuries table {
  width: (3.375rem * 3) + 2.625rem;

  td {
    padding: 0 0 1.25rem 1rem;

    @include app-rtl {
      padding: 0 1rem 1.25rem 0;
    }
  }
}

.datepicker-dropdown {
  left: 0;
  top: 0;
  padding: 0;
}

.input-daterange input {
  text-align: center;
}

// Light style
@if $enable-light-style {
  .light-style {
    .datepicker-dropdown {
      z-index: light.$zindex-popover !important;
      box-shadow: light.$card-box-shadow;
    }

    .datepicker {
      .cw {
        color: light.$headings-color;
      }
      &.datepicker-inline {
        table {
          box-shadow: light.$card-box-shadow;
        }
      }

      .next::after,
      .prev::after {
        color: light.$body-color;
      }

      table {
        thead {
          background: light.$dropdown-bg;
          tr,
          td {
            color: light.$headings-color;
          }
        }
        tr td,
        tr th {
          &.new {
            color: light.$text-muted;
          }
        }

        tr td {
          &.old,
          &.disabled {
            color: light.$text-muted;
          }

          &.day:hover,
          &.focused {
            background: light.$gray-50;
          }
          &.active,
          &.active.highlighted,
          .focused,
          span.active,
          span.active.disabled {
            box-shadow: light.$box-shadow-xs;
          }
        }
      }
    }

    .datepicker table tr td span {
      border-radius: light.$border-radius-lg;

      &:hover,
      &.focused {
        background: light.$gray-50;
      }

      &.disabled,
      &.disabled:hover {
        color: light.$text-muted;
      }

      &.old,
      &.new {
        color: light.$text-muted;
      }
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    .datepicker-dropdown {
      z-index: dark.$zindex-popover !important;
      box-shadow: dark.$card-box-shadow;
    }

    .datepicker {
      .cw {
        color: dark.$headings-color;
      }
      &.datepicker-inline {
        table {
          box-shadow: dark.$card-box-shadow;
        }
      }

      .next::after,
      .prev::after {
        color: dark.$body-color;
      }

      table {
        thead {
          background: dark.$dropdown-bg;
          tr,
          td {
            color: dark.$headings-color;
          }
        }
        tr td,
        tr th {
          &.new {
            color: dark.$text-muted;
          }
        }

        tr td {
          color: dark.$body-color;

          &.old,
          &.disabled {
            color: dark.$text-muted;
          }

          &.day:hover,
          &.focused {
            background: dark.$gray-50;
          }
          &.active,
          &.active.highlighted,
          .focused,
          span.active,
          span.active.disabled {
            box-shadow: dark.$box-shadow-xs;
          }
        }
      }
    }

    .datepicker table tr td span {
      border-radius: dark.$border-radius-lg;

      &:hover,
      &.focused {
        background: dark.$input-bg;
      }

      &.disabled,
      &.disabled:hover {
        color: dark.$text-light;
      }

      &.old,
      &.new {
        color: dark.$text-light;
      }
    }
  }
}
