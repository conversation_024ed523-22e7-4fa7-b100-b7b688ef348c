@import '../../scss/_bootstrap-extended/functions';

@mixin bs-select-theme($background, $color: null) {
  $color: if($color, $color, color-contrast($background));

  .bootstrap-select {
    .dropdown-menu.inner a[aria-selected='true'] {
      background: $background !important;
      color: $color !important;
    }
    // Fix: To add focus border, .bootstrap-select adding border but not able to update as we can not have the focus on div
    .dropdown-toggle.show {
      border-width: $input-focus-border-width;
      border-color: $background;
    }
  }
  // Form floating label color on hover/focus
  .form-floating:has(.bootstrap-select .dropdown-toggle.show) {
    label {
      color: $background;
    }
  }
  .form-floating.form-floating-bootstrap-select {
    label.form-floating-bootstrap-select-label {
      color: $background;
    }
  }
}
