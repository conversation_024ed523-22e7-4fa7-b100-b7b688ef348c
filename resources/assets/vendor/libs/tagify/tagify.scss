@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

@import 'mixins';

// Height clac to match form-control height
$tag-line-height: 1.5rem !default;
$tag-spacer: light.px-to-rem(floor(light.rem-to-px((light.$input-height - $tag-line-height) * 0.45))) !default;

// Override tagify vars
$tag-remove: light.$danger !default;
$tag-remove-btn-bg--hover: darken($tag-remove, 5) !default;
$tag-invalid-color: $tag-remove !default;
$tag-inset-shadow-size: 2em !default;
$tag-remove-btn-color: light.rgba-to-hex(light.$gray-600, light.$rgba-to-hex-bg) !default;
$tag-invalid-bg: rgba($tag-remove, 0.5) !default;

$tag-avatar-size: 22px !default;
$tag-avatar-select-size: 36px !default;

$tag-max-width: auto !default;

//! Tagify $tag-bg custom color to match with dark and light layout
$tag-bg: rgb(light.$text-light, 0.5) !default;

@import '@yaireo/tagify/src/tagify';

@import 'tagify-users-list';
@import 'tagify-inline-suggestion';
@import 'tagify-email-list';

.tagify {
  &.form-control {
    transition: none;
    padding: calc(light.$input-focus-border-width - light.$input-border-width) $tag-spacer $tag-spacer - 0.0625rem !important;
    .fv-plugins-bootstrap5-row-invalid &.is-invalid,
    .fv-plugins-bootstrap5-row-invalid &.is-valid {
      padding: 0 calc($tag-spacer - light.$input-border-width) calc($tag-spacer - light.$input-focus-border-width) !important;
    }
  }

  &.tagify--focus,
  &.form-control:focus {
    padding: 0 calc($tag-spacer - light.$input-border-width) calc($tag-spacer - light.$input-focus-border-width) !important;
    border-width: light.$input-focus-border-width;
  }
  &__tag,
  &__input {
    margin: $tag-spacer - 0.25rem $tag-spacer 0 0 !important;
    line-height: 1;
  }
  &__input {
    line-height: $tag-line-height;
    &:empty::before {
      top: 4px;
    }
  }
  &__tag {
    > div {
      line-height: $tag-line-height;
      padding: 0 0 0 $tag-spacer;
    }
    &__removeBtn {
      margin-right: $tag-spacer - 0.3rem;
      margin-left: $tag-spacer * 0.5;
      font-family: 'remixicon';
      opacity: 0.7;
      &:hover {
        background: none;
        color: $tag-remove-btn-bg--hover !important;
      }
      &::after {
        content: '\eb96';
      }
    }
    &:hover:not([readonly]),
    &:focus {
      div::before {
        top: 0px;
        right: 0px;
        bottom: 0px;
        left: 0px;
      }
    }
  }
  &__dropdown {
    transform: translateY(0);
  }
  &[readonly]:not(.tagify--mix) .tagify__tag > div {
    padding: 0 $tag-spacer 0 $tag-spacer !important;
  }
  &__input {
    padding: 0;
  }
  &__tag-text {
    font-size: light.$font-size-sm;
    font-weight: light.$font-weight-medium;
  }
}
// Floating (outline) label position on focus
.form-floating {
  .tagify {
    &.form-control {
      padding-top: calc(light.$form-floating-padding-y - $tag-spacer) !important;
    }
    &.tagify--focus,
    &.form-control:focus {
      padding-top: calc(light.$form-floating-padding-y - $tag-spacer - 1px) !important;
    }
  }
}

//RTL
@include app-rtl(false) {
  .tagify {
    &__tag,
    &__input {
      margin: $tag-spacer 0 0 $tag-spacer !important;
    }

    + input,
    + textarea {
      left: 0;
      right: -9999em !important;
    }

    &__tag {
      > div {
        padding: 0 $tag-spacer 0 0;
      }
      &__removeBtn {
        margin-left: $tag-spacer;
        margin-right: $tag-spacer * 0.5;
      }
    }
  }
}

// Light styles
@if $enable-light-style {
  .light-style {
    .tagify {
      &__tag {
        > div::before {
          box-shadow: 0 0 0 1.3em light.rgba-to-hex(light.$gray-75, light.$rgba-to-hex-bg) inset;
        }
        &:hover:not([readonly]),
        &:focus {
          div::before {
            box-shadow: 0 0 0 1.3em light.rgba-to-hex(light.$gray-75, light.$rgba-to-hex-bg) inset;
          }
        }
        .tagify__tag-text {
          color: light.$headings-color;
        }
        &__removeBtn {
          &:hover + div::before {
            background: rgba($tag-remove, 0.3);
          }
        }
      }
      &:hover:not([readonly]) {
        border-color: light.$input-border-color;
      }
      &__input::before {
        color: light.$input-placeholder-color !important;
      }
      &__dropdown {
        box-shadow: light.$dropdown-box-shadow;
        border-top-color: light.$dropdown-border-color;
        &__wrapper {
          background: light.$dropdown-bg;
          border-color: light.$dropdown-border-color;
        }
      }
    }
  }
}

// Dark styles
@if $enable-dark-style {
  .dark-style {
    .tagify {
      &__tag {
        > div {
          &::before {
            box-shadow: 0 0 0 1.3em dark.rgba-to-hex(dark.$gray-75, dark.$rgba-to-hex-bg) inset;
          }
          .tagify__tag-text {
            color: dark.$headings-color;
          }
        }
        &:hover:not([readonly]),
        &:focus {
          div::before {
            box-shadow: 0 0 0 1.3em dark.rgba-to-hex(dark.$gray-75, dark.$rgba-to-hex-bg) inset;
          }
        }
        &__removeBtn {
          color: dark.rgba-to-hex(dark.$gray-600, dark.$rgba-to-hex-bg);
          &:hover + div::before {
            background: rgba($tag-remove, 0.3);
          }
        }
      }
      &:hover:not([readonly]) {
        border-color: dark.$input-border-color;
      }
      &__input::before {
        color: dark.$input-placeholder-color !important;
      }
      &__dropdown {
        box-shadow: dark.$dropdown-box-shadow;
        border-top-color: dark.$dropdown-border-color;
        &__wrapper {
          background: dark.$dropdown-bg;
          border-color: dark.$dropdown-border-color;
        }
      }
    }
  }
}
