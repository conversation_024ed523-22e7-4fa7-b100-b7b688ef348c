@import './_components/include';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';

$primary-color: #666cff;
$body-bg: #f7f7f9;

body {
  background: $body-bg;
}

.bg-body {
  background: $body-bg !important;
}

@include template-common-theme($primary-color);
@include template-libs-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $headings-color, $active-color: $headings-color);

.layout-horizontal .layout-navbar {
  box-shadow: 0 1px 0 $border-color;
}

.layout-navbar-fixed .layout-page:not(.window-scrolled) .layout-navbar.navbar-detached {
  background: $body-bg;
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  #282a42,
  $color: #d7d8ee,
  $active-color: #d7d8ee,
  $active-bg: $primary-color
);

.app-brand .layout-menu-toggle {
  svg {
    path {
      fill: #eaeaff;
    }
  }
}
.menu-inner > .menu-header::before,
.menu-inner > .menu-header::after {
  background-color: #464964 !important;
}
.bg-menu-theme {
  .menu-header .menu-header-text {
    color: #7b7d95;
  }
  &.menu-vertical {
    .menu-item:not(.active) .menu-link:hover {
      background-color: rgba($body-bg, 0.06);
      color: #d7d8ee;
    }
    .menu-item.open {
      > .menu-link {
        background-color: rgba($body-bg, 0.08);
        color: #d7d8ee;
      }
    }
  }
  &.menu-horizontal {
    .menu-inner .menu-item:not(.menu-item-closing) > .menu-sub {
      background: #282a42;
    }
    .menu-inner > .menu-item .menu-sub .menu-item.active > .menu-link.menu-toggle {
      background-color: rgba($body-bg, 0.08);
    }
    .menu-item .menu-link:hover {
      background-color: rgba($body-bg, 0.06);
      color: #d7d8ee;
    }
  }
  .menu-inner > .menu-item .menu-link.menu-toggle:after {
    color: #d7d8ee;
  }
}
// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', $body-bg, $color: $primary-color, $active-color: $headings-color);
