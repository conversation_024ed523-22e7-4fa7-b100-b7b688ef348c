// Tables
// ********************************************************************************/

@each $color, $value in $table-variants {
  @if $color != primary {
    @include template-table-variant($color, $value);
  }
}

// Firefox fix for table head border bottom
.table {
  > :not(caption) > * > * {
    background-clip: padding-box;
  }
  tr {
    > td {
      .dropdown {
        position: static;
      }
    }
  }
  caption {
    padding: $table-cell-padding-y $table-cell-padding-x;
  }
  &.table-sm {
    thead tr th {
      padding-block: $table-head-padding-y-sm;
    }
  }
  // Table heading style
  th {
    text-transform: uppercase;
    font-size: $font-size-sm;
    letter-spacing: 0.2px;
  }
  thead tr th {
    border-bottom-color: $table-header-bg-color;
    padding-block: $table-head-padding-y;
  }
  // heading background color except .table-dark and .table-light
  &:not(.table-dark):not(.table-light) thead:not(.table-dark):not(.table-light) tr th {
    color: $table-th-color;
    background-color: $table-header-bg-color;
  }
  &:not(.table-dark) tbody:last-child tr:last-child > td {
    padding-bottom: $table-cell-padding-y + 0.03125rem;
  }
}

// Style for table inside card
.card {
  .table {
    margin-bottom: 0;
  }
}
@supports (-moz-appearance: none) {
  .table {
    .dropdown-menu.show {
      display: inline-table;
    }
  }
}
.table-dark th {
  color: var(--#{$prefix}table-color);
}
@if $dark-style {
  .table-light th {
    color: var(--#{$prefix}table-color);
  }
}

// Dark Table icon button
.table.table-dark .btn.btn-icon {
  color: $table-border-color;
}

// class for to remove table border bottom
.table-border-bottom-0 {
  tr:last-child {
    td,
    th {
      border-bottom-width: 0;
    }
  }
}

// .table {
//   .btn-group {
//     display: block;
//   }
// }

// Flush spacing of left from first column ans right from last column
.table.table-flush-spacing {
  thead,
  tbody {
    tr > td:first-child {
      padding-left: 0;
    }
    tr > td:last-child {
      padding-right: 0;
    }
  }
}

// * Table inside card
// *******************************************************************************

// .card,
.nav-align-top,
.nav-align-right,
.nav-align-bottom,
.nav-align-left {
  .table:not(.table-dark),
  .table:not(.table-dark) thead:not(.table-dark) th,
  .table:not(.table-dark) tfoot:not(.table-dark) th,
  .table:not(.table-dark) td {
    border-color: $border-inner-color;
  }
}
