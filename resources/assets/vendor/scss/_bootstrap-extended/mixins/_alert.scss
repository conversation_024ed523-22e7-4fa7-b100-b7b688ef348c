// Alerts
// *******************************************************************************

@mixin alert-variant($background: null, $border: null, $color: null) {
}

// Basic Alerts
@mixin template-alert-variant($parent, $background) {
  $border: if(
    $dark-style,
    shift-color($background, -$alert-border-scale, $card-bg),
    shift-color($background, $alert-border-scale, $card-bg)
  );
  $color: $background;
  $background: if(
    $dark-style,
    shift-color($background, -$alert-bg-scale, $card-bg),
    shift-color($background, $alert-bg-tint-scale, $card-bg)
  );

  #{$parent} {
    @include gradient-bg($background);
    border-color: $border;
    color: $color;
    .btn-close {
      background-image: str-replace(str-replace($btn-close-bg, '#{$btn-close-color}', $color), '#', '%23');
    }

    .alert-link {
      color: $color;
    }
  }

  #{$parent} {
    hr {
      background-color: $color !important;
    }
    .alert-icon {
      background-color: $color;
    }
  }
}

// Solid Alerts
@mixin template-alert-solid-variant($parent, $background, $color: null) {
  $color: if($color, $color, color-contrast($background));

  #{$parent} {
    @include gradient-bg($background);
    color: $color;

    .btn-close {
      background-image: str-replace(str-replace($btn-close-bg, '#{$btn-close-color}', $color), '#', '%23');
    }

    .alert-link {
      color: $color;
    }
  }

  #{$parent} {
    hr {
      background-color: $color !important;
    }
    .alert-icon {
      color: $background !important;
    }
  }
}

// Outline Alerts
@mixin template-alert-outline-variant($parent, $background, $color: null) {
  $color: $background;
  $icon-background: if(
    $dark-style,
    shift-color($background, -$alert-bg-scale, $card-bg),
    shift-color($background, $alert-bg-tint-scale, $card-bg)
  );

  #{$parent} {
    border-color: $background;
    color: $color;
    .btn-close {
      background-image: str-replace(str-replace($btn-close-bg, '#{$btn-close-color}', $background), '#', '%23');
    }

    .alert-link {
      color: $color;
    }
  }

  #{$parent} {
    hr {
      background-color: $color !important;
    }
    .alert-icon {
      color: $color !important;
      background-color: $icon-background !important;
    }
  }
}
