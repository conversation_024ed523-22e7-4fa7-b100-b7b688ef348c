// Select
// *******************************************************************************

.form-select {
  background-clip: padding-box;
  optgroup {
    background-color: $card-bg;
  }
  &:hover {
    &:not([disabled]):not([focus]) {
      border-color: $input-border-hover-color;
    }
  }
  &:disabled {
    @include ltr-style {
      background-image: escape-svg($form-select-disabled-indicator);
    }
    @include rtl-style {
      background-image: escape-svg($form-select-disabled-indicator);
    }
  }
  @include ltr-style {
    padding: calc($form-select-padding-y - $form-select-border-width)
      calc($form-select-padding-x * 3 - $form-select-border-width)
      calc($form-select-padding-y - $form-select-border-width) calc($form-select-padding-x - $form-select-border-width);
  }
  @include rtl-style {
    padding: calc($form-select-padding-y - $form-select-border-width)
      calc($form-select-padding-x - $form-select-border-width) calc($form-select-padding-y - $form-select-border-width)
      calc($form-select-padding-x * 3 - $form-select-border-width);
  }
  &:focus {
    border-width: $input-focus-border-width;
    @include ltr-style {
      padding: calc($form-select-padding-y - $input-focus-border-width)
        calc($form-select-padding-x * 3 - $input-focus-border-width)
        calc($form-select-padding-y - $input-focus-border-width)
        calc($form-select-padding-x - $input-focus-border-width);
    }
    @include rtl-style {
      padding: calc($form-select-padding-y - $input-focus-border-width)
        calc($form-select-padding-x - $input-focus-border-width)
        calc($form-select-padding-y - $input-focus-border-width)
        calc($form-select-padding-x * 3 - $input-focus-border-width);
    }
    background-position: right calc($form-select-padding-x - 1px) center;
  }
  &.form-select-lg {
    min-height: $input-height-lg;
    background-size: 24px 24px;
    @include ltr-style {
      padding: calc($form-select-padding-y-lg - $form-select-border-width)
        calc($form-select-padding-x-lg * 3 - $form-select-border-width)
        calc($form-select-padding-y-lg - $form-select-border-width)
        calc($form-select-padding-x-lg - $form-select-border-width);
    }
    @include rtl-style {
      padding: calc($form-select-padding-y-lg - $form-select-border-width)
        calc($form-select-padding-x-lg - $form-select-border-width)
        calc($form-select-padding-y-lg - $form-select-border-width)
        calc($form-select-padding-x-lg * 3 - $form-select-border-width);
    }
    &:focus {
      @include ltr-style {
        padding: calc($form-select-padding-y-lg - $input-focus-border-width)
          calc($form-select-padding-x-lg * 3 - $input-focus-border-width)
          calc($form-select-padding-y-lg - $input-focus-border-width)
          calc($form-select-padding-x-lg - $input-focus-border-width);
      }
      @include rtl-style {
        padding: calc($form-select-padding-y-lg - $input-focus-border-width)
          calc($form-select-padding-x-lg - $input-focus-border-width)
          calc($form-select-padding-y-lg - $input-focus-border-width)
          calc($form-select-padding-x-lg * 3 - $input-focus-border-width);
      }
    }
  }
  &.form-select-sm {
    min-height: $input-height-sm;
    background-size: 20px 20px;
    @include ltr-style {
      padding: calc($form-select-padding-y-sm - $form-select-border-width)
        calc($form-select-padding-x-sm * 3 - $form-select-border-width)
        calc($form-select-padding-y-sm - $form-select-border-width)
        calc($form-select-padding-x-sm - $form-select-border-width);
    }
    @include rtl-style {
      padding: calc($form-select-padding-y-sm - $form-select-border-width)
        calc($form-select-padding-x-sm - $form-select-border-width)
        calc($form-select-padding-y-sm - $form-select-border-width)
        calc($form-select-padding-x-sm * 3 - $form-select-border-width);
    }
    &:focus {
      @include ltr-style {
        padding: calc($form-select-padding-y-sm - $input-focus-border-width)
          calc($form-select-padding-x-sm * 3 - $input-focus-border-width)
          calc($form-select-padding-y-sm - $input-focus-border-width)
          calc($form-select-padding-x-sm - $input-focus-border-width);
      }
      @include rtl-style {
        padding: calc($form-select-padding-y-sm - $input-focus-border-width)
          calc($form-select-padding-x-sm - $input-focus-border-width)
          calc($form-select-padding-y-sm - $input-focus-border-width)
          calc($form-select-padding-x-sm * 3 - $input-focus-border-width);
      }
    }
  }
}

// Multiple select RTL Only
@include rtl-only {
  .form-select {
    background-image: $form-select-indicator-rtl;
    background-position: left $form-select-padding-x center;
    &:focus {
      background-position: left calc($form-select-padding-x - 1px) center;
    }
    &[multiple],
    &[size]:not([size='1']) {
      background-image: none;
    }
  }
}
@if $dark-style {
  select.form-select {
    option {
      background-color: $card-bg;
    }
    &:hover {
      &:not([disabled]):not([focus]) {
        border-color: rgba-to-hex($gray-600, $rgba-to-hex-bg);
      }
    }
  }
}
