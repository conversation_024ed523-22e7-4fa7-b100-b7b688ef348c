// Variables
//
// Variables should follow the `$component-state-property-size` formula for
// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.
//
// (C) Custom variables for extended components of bootstrap only

// ! _variable-dark.scss file overrides _variable.scss file.

// * Colors
// *******************************************************************************

// scss-docs-start gray-color-variables
$white: #fff !default;
$black: #30334e !default;

$base: #eaeaff !default;
$gray-25: rgba($base, 0.015) !default; // (C)
$gray-50: rgba($base, 0.03) !default; // (C)
$gray-75: rgba($base, 0.079) !default; // (C)
$gray-100: rgba($base, 0.06) !default;
$gray-200: rgba($base, 0.12) !default;
$gray-300: rgba($base, 0.22) !default;
$gray-400: rgba($base, 0.401) !default;
$gray-500: rgba($base, 0.5) !default;
$gray-600: rgba($base, 0.599) !default;
$gray-700: rgba($base, 0.7) !default;
$gray-800: rgba($base, 0.8) !default;
$gray-900: rgba($base, 0.9) !default;
// scss-docs-end gray-color-variables

// scss-docs-start gray-colors-map
$grays: (
  '25': $gray-25,
  '50': $gray-50
) !default;
// scss-docs-end gray-colors-map

// scss-docs-start color-variables
$blue: #26c6f9 !default;
$indigo: #666cff !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #ff4d49 !default;
$orange: #fdb528 !default;
$yellow: #ffd950 !default;
$green: #72e128 !default;
$teal: #20c997 !default;
$cyan: #28c3d7 !default;
// scss-docs-end color-variables

// scss-docs-start theme-color-variables
$primary: $indigo !default;
$secondary: #6d788d !default;
$success: $green !default;
$info: $blue !default;
$warning: $orange !default;
$danger: $red !default;
$light: #46445b !default;
$dark: #d7d5ec !default;
$gray: $gray-100 !default; // (C)
// scss-docs-end theme-color-variables

// scss-docs-start theme-colors-map
$theme-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark,
  'gray': $gray
) !default;
// scss-docs-end theme-colors-map

$color-scheme: 'dark' !default; // (C)

// * Body
// *******************************************************************************

$body-bg: #282a42 !default;
$rgba-to-hex-bg: $black !default; // (C)
$body-color: rgba-to-hex($gray-700, $rgba-to-hex-bg) !default;
$rgba-to-hex-bg-inverted: rgb(160, 149, 149) !default; // (C)

// * Components
// *******************************************************************************

$alert-color-scale: -10% !default;

// $border-color: rgba-to-hex(rgba($white, 0.1), $rgba-to-hex-bg) !default;
// $border-inner-color: rgba-to-hex(rgba($white, 0.09), $rgba-to-hex-bg) !default; // (C)
$border-color: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;
$border-inner-color: rgba($white, 0.09) !default; // (C)

// scss-docs-start box-shadow-variables
$shadow-bg: #101121 !default; // (C)
$box-shadow: 0 0.25rem 0.875rem 0 rgba($shadow-bg, 0.26) !default;
$box-shadow-xs: 0 0.125rem 0.375rem 0 rgba($shadow-bg, 0.2) !default;
$box-shadow-sm: 0 0.125rem 0.625rem 0 rgba($shadow-bg, 0.24) !default;
$box-shadow-lg: 0 0.375rem 1.25rem 0 rgba($shadow-bg, 0.28) !default;
$box-shadow-xl: 0 0.5rem 1.625rem 0 rgba($shadow-bg, 0.3) !default;
// scss-docs-end box-shadow-variables

$floating-component-border-color: rgba($white, 0.05) !default; // (C)
// $floating-component-shadow: 0 1px 16px 1px rgba($white, 0.09) !default; // (C)
$floating-component-shadow:
  0px 5px 5px -3px rgba($black, 0.2),
  0px 8px 10px 1px rgba($black, 0.14),
  0px 3px 14px 2px rgba($black, 0.12) !default; // (C)

// * Typography
// *******************************************************************************

$text-muted: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;
$text-muted-hover: rgba-to-hex($white, $rgba-to-hex-bg) !default; // (C)

$text-light: rgba-to-hex($gray-500, $rgba-to-hex-bg) !default; // (C)
$text-lighter: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default; // (C)
$text-lightest: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)

$light-gray-bg: rgba-to-hex($white, $rgba-to-hex-bg) !default; // (C)

$headings-color: rgba-to-hex($gray-900, $rgba-to-hex-bg) !default;

// * Tables
// *******************************************************************************

$table-bg-scale: -80% !default;
$table-hover-bg-factor: 0.06 !default;
$table-hover-bg: rgba($base, $table-hover-bg-factor) !default;

$table-striped-bg-factor: 0.025 !default;
$table-striped-bg: rgba-to-hex(rgba($white, $table-striped-bg-factor), $rgba-to-hex-bg) !default;

$table-active-color: $body-color !default;
$table-active-bg-factor: 0.08 !default;
$table-active-bg: rgba($primary, $table-active-bg-factor) !default;

$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;

$table-border-color: $border-color !default;
$table-group-separator-color: $table-border-color !default;

$component-active-bg: $white !default;

$table-header-bg-color: #3a3e5b !default; // (c)

// * Pagination
// *******************************************************************************

$pagination-bg: rgba-to-hex(rgba($base, 0.08), $rgba-to-hex-bg) !default;
$pagination-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;
$pagination-disabled-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;

// * Cards
// *******************************************************************************

$card-bg: $black !default;
$card-subtitle-color: rgba-to-hex(rgba($base, 0.55), $rgba-to-hex-bg) !default;
$card-border-color: $border-color !default;

// * Accordion
// *******************************************************************************

$accordion-bg: $card-bg !default;

// * Buttons
// *******************************************************************************

$btn-label-bg-tint-amount: 84% !default; // (C)
$btn-fab-box-shadow:
  0px 3px 5px -1px rgba($black, 0.2),
  0px 5px 8px rgba($black, 0.14),
  0px 1px 14px rgba($black, 0.12) !default;
$btn-fab-active-box-shadow:
  0px 7px 8px -4px rgba($black, 0.2),
  0px 12px 17px 2px rgba($black, 0.14),
  0px 5px 22px 4px rgba($black, 0.12) !default;

// * Forms
// *******************************************************************************

$input-bg: transparent !default;

// $input-border-color: rgba($white, 0.15) !default;
$input-border-color: rgba-to-hex(rgba($base, 0.22), $rgba-to-hex-bg) !default;
$input-border-hover-color: rgba-to-hex($gray-600, $rgba-to-hex-bg) !default; // (C)

$input-placeholder-color: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;

$form-floating-hover-bg: rgba-to-hex(rgba($base, 0.079), $rgba-to-hex-bg) !default; // (C)

$form-check-input-disabled-bg: rgba-to-hex(rgba($base, 0.305), $rgba-to-hex-bg) !default; // (C)

$form-switch-bg: rgba-to-hex(rgba($base, 0.1), $rgba-to-hex-bg) !default; // (C)

$form-select-bg: $input-bg !default;
$form-select-indicator: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="#{$base}" fill-opacity="0.9"/></svg>') !default;

$form-range-thumb-bg: rgba-to-hex(rgba($white, 0.5), $rgba-to-hex-bg) !default;

// * Navs
// *******************************************************************************

$nav-tabs-link-active-bg: $card-bg !default;
$nav-tabs-link-active-border-color: $nav-tabs-link-active-bg !default;

// * Navbar
// *******************************************************************************

$navbar-light-hover-color: #4e5155 !default;
$navbar-light-active-color: #4e5155 !default;
$navbar-light-disabled-color: rgba($black, 0.2) !default;

// * Dropdowns
// *******************************************************************************

// $dropdown-bg: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;
$dropdown-bg: $card-bg !default;
$dropdown-divider-bg: $border-inner-color !default;

// * Tooltips
// *******************************************************************************

$tooltip-bg: #f5f5f5 !default;
$tooltip-color: $black !default;

// * Toasts
// *******************************************************************************

$toast-box-shadow: $box-shadow-lg !default;

// * Modals
// *******************************************************************************

$modal-content-bg: $card-bg !default;
$modal-content-box-shadow-xs: $box-shadow-lg;

$modal-backdrop-bg: #101121 !default;
// * List group
// *******************************************************************************

$list-group-border-color: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default;
$list-group-item-bg-scale: -84% !default;
$list-group-item-bg-hover-scale: 6% !default; //  (c)

// Close
// *******************************************************************************
$btn-close-color: $white !default;

$kbd-color: $dark !default;

// Config
$rtl-support: false !default;
$dark-style: true !default;
