// Tooltips
// *******************************************************************************

@each $color, $value in $theme-colors {
  @if $color != primary and $color != light {
    @include template-tooltip-variant(
      '.tooltip-#{$color}, .tooltip-#{$color} > .tooltip',
      rgba-to-hex(if($color== 'dark' and $dark-style, $light, $value), $rgba-to-hex-bg)
    );
  }
}

// fonr weight for tooltip text
.tooltip .tooltip-inner {
  font-weight: $font-weight-medium;
}

// Open modal tooltip z-index
.modal-open .tooltip {
  z-index: $zindex-modal + 2;
}

// RTL
// *******************************************************************************

@include rtl-only {
  .tooltip {
    text-align: right;
  }
  &.bs-tooltip-auto {
    &[data-popper-placement='right'] {
      .tooltip-arrow {
        right: 0;
        left: inherit;
        &::before {
          left: -1px;
          border-width: ($tooltip-arrow-width * 0.5) 0 ($tooltip-arrow-width * 0.5) $tooltip-arrow-height;
          border-left-color: $tooltip-arrow-color;
        }
      }
    }
    &[data-popper-placement='left'] {
      .tooltip-arrow {
        left: 0;
        right: inherit;
        &::before {
          right: -1px;
          border-width: ($tooltip-arrow-width * 0.5) $tooltip-arrow-height ($tooltip-arrow-width * 0.5) 0;
          border-right-color: $tooltip-arrow-color;
        }
      }
    }
  }
}
