// scss
.section-py {
  padding: 6.25rem 0;
  @include light.media-breakpoint-down(xl) {
    padding: 4rem 0;
  }
  @include light.media-breakpoint-down(md) {
    padding: 3rem 0;
  }
}

.first-section-pt {
  padding-top: 10.3rem;
  @include light.media-breakpoint-down(xl) {
    padding-top: 6.5rem;
  }
}

.card {
  // card hover border color
  &[class*='card-hover-border-'] {
    transition: light.$card-transition;
  }
}

.banner-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: left;
  z-index: -1;
}

.bg-icon-left,
.bg-icon-right {
  position: relative;
  &::before {
    position: absolute;
    display: block;
    top: 0;
  }
}
.bg-icon-left {
  &::before {
    left: 0;
    @include light.media-breakpoint-down(sm) {
      left: 0.625rem;
    }
  }
}
.bg-icon-right {
  &::before {
    right: 0;
    @include light.media-breakpoint-down(sm) {
      right: 0.625rem;
    }
  }
}

.dropdown-toggle::after {
  margin-top: -0.28em;
  width: 0.42em;
  height: 0.42em;
  border: 1px solid !important;
  border-top: 0 !important;
  border-left: 0 !important;
  transform: rotate(45deg);
}

// Light style
@if $enable-light-style {
  .light-style {
    body {
      background-color: light.$card-bg;
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    body {
      background-color: dark.$card-bg;
    }
    .landing-light-mode {
      display: none;
    }
    .landing-dark-mode {
      display: block;
    }
  }
}
