// * App Email
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_components/include';
@import '../_custom-variables/pages';

$email-sidebar-width: 16.25rem;
$email-app-height: calc(100vh - 10.5rem);
$email-app-height-with-no-navbar: calc(100vh - 6.5rem);
$email-app-horizontal-height-diff: 5rem;
$email-list-item-meta-padding-left: 2rem;
$email-view-width: 16.2rem;
$email-padding-x: 1rem;
$email-padding-y: 0.6rem;
$email-filter-padding-y: 0.3125rem;
$email-filter-padding-x: 1.25rem;

.app-email {
  position: relative;
  height: $email-app-height !important;
  overflow: hidden;
  .layout-navbar-hidden & {
    height: $email-app-height-with-no-navbar !important;
  }
  @include light.media-breakpoint-down(md) {
    height: calc($email-app-height - 0.5rem) !important;
  }
  @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    .layout-horizontal & {
      height: calc($email-app-height - $email-app-horizontal-height-diff) !important;
    }
  }

  // Email sidebar
  .app-email-sidebar {
    position: absolute;
    left: calc(-#{$email-sidebar-width} - 1.2rem);
    width: $email-sidebar-width;
    height: 100%;
    z-index: 4;
    flex-basis: $email-sidebar-width;
    transition: all 0.2s;

    .btn-compost-wrapper {
      padding: 1.25rem 1.25rem;
    }

    &.show {
      left: 0;
    }

    .email-filters {
      height: calc(100vh - 14.3rem);
      .layout-navbar-hidden & {
        height: calc(100vh - 11rem);
      }
      @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
        .layout-horizontal & {
          height: calc(100vh - 14rem - $email-app-horizontal-height-diff);
        }
      }

      .email-filter-folders {
        li {
          &:first-child {
            margin-top: 0;
          }
          &:last-child {
            margin-bottom: 0;
          }
          &.active {
            border-color: light.$primary;
          }
        }
      }

      li {
        padding: $email-filter-padding-y $email-filter-padding-x;
        border-left: 3px solid transparent;

        h6 {
          font-size: 1rem;
        }
      }
    }
  }

  // Email compose
  .app-email-compose {
    .modal-dialog {
      position: fixed;
      bottom: 0;
      right: 0;
      width: 100%;
    }
    .email-compose-to {
      .select2-selection {
        border: none;
        padding: 0.5rem 1rem !important;
        min-height: 3rem;
        .select2-selection__choice {
          padding-top: 0.2rem;
          padding-bottom: 0.2rem;
          display: flex;
          align-items: center;
          line-height: 0;
        }
      }
    }

    .email-compose-toggle-wrapper {
      width: 80px;
    }

    .email-compose-subject,
    .email-compose-cc,
    .email-compose-bcc {
      .form-control:focus {
        padding-block: calc(light.$input-padding-y - light.$input-border-width);
      }
    }
    .email-compose-cc,
    .email-compose-bcc {
      .form-control:focus {
        padding-left: calc(light.$input-padding-x - light.$input-border-width);
      }
    }
    .ql-editor {
      height: 10rem;
      min-height: 10rem;
      padding-inline: $email-filter-padding-x + 0.25;
    }
    .ql-snow.ql-toolbar {
      padding: 0.5rem 1rem;
    }
    .ql-editor.ql-blank::before {
      padding-left: 1.5rem;
    }
  }

  // Email list
  .app-emails-list {
    .emails-list-header {
      .emails-list-header-hr {
        margin-top: 0rem;
      }
    }
    .email-list {
      @include light.media-breakpoint-up(lg) {
        height: calc(100vh - 17rem);
      }
      @include light.media-breakpoint-down(lg) {
        height: calc(100vh - 17.4rem);
      }
      .layout-navbar-hidden & {
        @include light.media-breakpoint-up(lg) {
          height: calc(100vh - 13rem);
        }
        @include light.media-breakpoint-down(lg) {
          height: calc(100vh - 13.5rem);
        }
      }
      @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
        .layout-horizontal & {
          height: calc(100vh - 15rem - 7rem);
        }
      }

      li.email-list-item {
        padding: $email-padding-y $email-padding-x;
        min-height: 3.75rem;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
        z-index: 1;

        .email-list-item-subject {
          font-size: 0.8125rem;
        }

        .email-list-item-username {
          font-weight: light.$font-weight-medium;
        }
        .email-list-item-time {
          width: 64px;
          display: inline-block;
          text-align: right;
        }

        .email-list-item-meta {
          .email-list-item-actions {
            display: none;

            li {
              padding: 0;
              margin-right: 0 !important;
            }
          }
        }
        .list-inline-item {
          &:not(:last-child) {
            margin-inline-end: 0.25rem;
          }
        }

        &.email-list-item:not(.list-inline-item):hover {
          z-index: 5;
          transform: translateY(-2px);
        }
      }
    }
  }

  // Email view
  .app-email-view {
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: $email-app-height;
    z-index: -1;
    transition: all 0.25s ease;
    .layout-navbar-hidden & {
      height: $email-app-height-with-no-navbar;
    }
    .layout-horizontal & {
      @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
        height: calc($email-app-height - $email-app-horizontal-height-diff);
      }
    }

    &.show {
      right: 0;
      z-index: 4;
      @include light.media-breakpoint-up(lg) {
        right: -1px;
      }
    }

    .app-email-view-header {
      padding-bottom: 1.3rem;
    }

    .app-email-view-content {
      @include light.media-breakpoint-up(md) {
        height: calc(100vh - 17.9rem);
        .layout-navbar-hidden & {
          height: calc(100vh - 13.9rem);
        }
      }
      @include light.media-breakpoint-down(md) {
        height: calc(100vh - 17.9rem);
      }
      @include light.media-breakpoint-up($menu-collapsed-layout-breakpoint) {
        .layout-horizontal & {
          height: calc(100vh - 17.9rem - $email-app-horizontal-height-diff);
        }
      }

      .ql-container {
        border: 0;

        .ql-editor {
          height: 7rem;
          min-height: 7rem;
        }
      }
      .ql-editor,
      .ql-editor.ql-blank::before {
        padding-left: 0.5rem;
      }

      .email-card-prev {
        display: none;
      }

      .email-card-last {
        transition: all 0.25s ease-in-out;

        &:before {
          position: absolute;
          bottom: 1rem;
          left: 2.5rem;
          right: 2.5rem;
          top: -2rem;
          z-index: -1;
          content: '';
          border-radius: light.$border-radius-lg;
        }

        &:after {
          position: absolute;
          bottom: 0.5rem;
          left: 1rem;
          right: 1rem;
          top: -1rem;
          z-index: -1;
          content: '';
          border-radius: light.$border-radius-lg;
        }

        &.hide-pseudo {
          &:before,
          &:after {
            display: none !important;
          }
        }
      }
    }
  }

  // Responsive style
  @media (min-width: 1199px) {
    .email-list li {
      .email-list-item-meta {
        margin-right: 0.45rem;
      }
    }
  }

  @media (min-width: 992px) {
    .app-email-sidebar {
      position: static;
      height: auto;
    }

    .email-list {
      li.email-list-item:hover {
        .email-list-item-meta {
          .email-list-item-attachment,
          .email-list-item-time,
          .email-list-item-label {
            display: none !important;
          }

          .email-list-item-actions {
            display: block;
            text-wrap: nowrap;
          }
        }
      }
    }

    .app-email-view {
      width: calc(100% - #{$email-view-width});
    }
  }

  @media (max-width: 576px) {
    .app-emails-list {
      .emails-list-header {
        padding: 1rem;
      }

      .email-list {
        li.email-list-item {
          padding: 1rem;

          .email-list-item-username {
            font-size: 0.85rem;
          }
          .email-list-item-user {
            min-width: auto !important;
            margin-right: 1rem;
          }
        }
      }
    }

    .app-email-view {
      .email-list-item-username {
        font-size: 1rem;
      }
    }
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    .app-email {
      .app-email-sidebar {
        background-color: light.$card-bg;

        ul {
          li {
            &:not(.active) {
              a {
                color: light.$headings-color;
              }
            }
          }
        }
      }

      .email-list {
        li.email-list-item {
          .email-list-item-user {
            min-width: 12rem;
          }
          &.email-marked-read {
            background-color: light.rgba-to-hex(light.$gray-100, light.$rgba-to-hex-bg);
          }

          &:hover {
            box-shadow: light.$box-shadow-sm;
          }

          .email-list-item-actions li {
            box-shadow: none;
          }

          &:not(:first-child) {
            border-top: 1px solid light.$border-color;
          }

          &[data-starred='true'] {
            .email-list-item-bookmark {
              color: light.$warning;
            }
          }
        }
      }

      .app-email-view {
        .email-card-last {
          border: 1px solid light.$border-color;
          &:before {
            background-color: rgba(light.$card-bg, 0.4);
            border: 1px solid light.$border-color;
          }

          &:after {
            background-color: rgba(light.$card-bg, 0.7);
            border: 1px solid light.$border-color;
          }
        }
        .app-email-view-header {
          background-color: light.$card-bg;
        }
        .email-reply {
          border: 1px solid light.$border-color;
        }
      }
      .app-email-compose .modal-dialog .modal-header {
        background-color: light.$gray-100;
      }
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    .app-email {
      .app-email-sidebar {
        background-color: dark.$card-bg;
        ul {
          li {
            &:not(.active) {
              a {
                color: dark.$headings-color;
              }
            }
          }
        }
      }

      .emails-list-header {
        .email-search-input,
        .input-group-text {
          background-color: transparent;
        }
      }

      .email-list {
        li.email-list-item {
          &.email-marked-read {
            background-color: dark.rgba-to-hex(dark.$gray-100, dark.$rgba-to-hex-bg);
          }

          &:not(:first-child) {
            border-top: 1px solid dark.$border-color;
          }

          &:hover {
            box-shadow: dark.$box-shadow-sm;
          }

          .email-list-item-actions li {
            box-shadow: none;
          }
          &[data-starred='true'] {
            .email-list-item-bookmark {
              color: dark.$warning;
            }
          }
        }
      }

      .app-email-view {
        .email-card-last {
          border: 1px solid dark.$border-color;
          &:before {
            background-color: rgba(dark.$card-bg, 0.4);
            border: 1px solid dark.$border-color;
          }

          &:after {
            background-color: rgba(dark.$card-bg, 0.7);
            border: 1px solid dark.$border-color;
          }
        }
        .app-email-view-header {
          background-color: dark.$card-bg;
        }
        .email-reply {
          border: 1px solid dark.$border-color;
        }
      }
      .app-email-compose .modal-dialog .modal-header {
        background-color: dark.$gray-100;
      }
    }
  }
}

// RTL
@if $enable-rtl-support {
  [dir='rtl'] {
    .app-email {
      .app-emails-list {
        .emails-list-header {
          .dropdown-menu-end {
            right: auto !important;
            left: 0 !important;
          }
        }
      }

      .app-email-sidebar {
        right: calc(-#{$email-sidebar-width} - 1.2rem);
        left: auto;
        &.show {
          right: 0;
          left: auto;
        }

        .email-filters {
          li {
            border-left: 0;
            border-right: 3px solid transparent;
          }
        }
      }

      .app-email-compose {
        .modal-dialog {
          left: 0;
          right: auto;
        }
      }

      .app-email-view {
        right: auto;
        left: -100%;

        &.show {
          right: auto;
          left: 0;
          @include light.media-breakpoint-up(lg) {
            left: -1px;
          }
        }

        .app-email-view-header {
          .ri-arrow-right-s-line,
          .ri-arrow-left-s-line {
            transform: rotate(180deg);
          }
        }
      }
    }

    @media (min-width: 1199px) {
      .email-list li {
        .email-list-item-meta {
          text-align: left;
        }
      }
    }

    @media (max-width: 576px) {
      .app-emails-list {
        .email-list {
          li.email-list-item {
            .email-list-item-user {
              margin-left: 1rem;
            }
          }
        }
      }
    }
    .ps--active-y > .ps__rail-y {
      right: inherit !important;
    }
  }
}
