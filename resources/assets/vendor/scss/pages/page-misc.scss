// * Miscellaneous
// *******************************************************************************

@import '../_bootstrap-extended/include';
@import '../_custom-variables/pages';

// Misc wrapper styles
.misc-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  justify-content: center;
  position: relative;
  padding: 1.25rem;
}

// Misc background image styles
.misc-bg {
  inline-size: 100%;
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
}

// Misc object styles
.misc-object {
  position: absolute;
  bottom: 8%;
  z-index: 1;
  @include app-ltr() {
    left: 16%;
  }
  @include app-rtl() {
    right: 10%;
  }
}
