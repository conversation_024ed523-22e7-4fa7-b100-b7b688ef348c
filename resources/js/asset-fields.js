/**
 * Asset Fields Management JavaScript
 * Handles CRUD operations for asset fields with AJAX, field preview, and options management
 */

// Global variables
let assetFieldsTable;
let deleteAssetFieldId = null;
let isEditMode = false;
let optionIndex = 0;

// Initialize when DOM is ready
$(document).ready(function() {
  // Check if routes are available
  if (!checkRoutes(window.assetFieldsRoutes, 'Asset Fields')) {
    return;
  }

  initializeDataTable();
  initializeEventHandlers();
});

/**
 * Initialize DataTable for asset fields
 */
function initializeDataTable() {
  assetFieldsTable = $('#assetFieldsTable').DataTable({
    processing: true,
    ajax: {
      url: window.assetFieldsRoutes.index,
      dataSrc: 'data',
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi x<PERSON>y ra khi tải dữ liệu');
      }
    },
    columns: [
      {
        data: 'name',
        render: function(data, type, row) {
          return `<code class="text-primary">${data}</code>`;
        }
      },
      {
        data: 'label',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      {
        data: 'type_formatted',
        render: function(data, type, row) {
          const typeClass = `field-type-${row.type}`;
          return `<span class="badge field-type-badge ${typeClass}">${data}</span>`;
        }
      },
      {
        data: 'required_badge',
        orderable: false,
        className: 'text-center'
      },
      {
        data: 'status_badge',
        orderable: false,
        className: 'text-center'
      },
      {
        data: 'templates_count',
        orderable: false,
        className: 'text-center',
        render: function(data, type, row) {
          return `<span class="badge bg-label-info">${data}</span>`;
        }
      },
      {
        data: 'sort_order',
        className: 'text-center'
      },
      {
        data: 'action',
        orderable: false,
        searchable: false,
        className: 'text-center'
      }
    ],
    order: [[6, 'asc'], [1, 'asc']], // Sort by sort_order, then label
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
    drawCallback: function(settings) {
      // Add fade-in animation to new rows
      $('#assetFieldsTable tbody tr').addClass('fade-in');
    }
  });
}

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
  // Form submission
  $('#assetFieldForm').on('submit', function(e) {
    e.preventDefault();
    saveAssetField();
  });

  // Modal reset when hidden
  $('#assetFieldModal').on('hidden.bs.modal', function() {
    resetForm();
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    if (deleteAssetFieldId) {
      performDeleteAssetField(deleteAssetFieldId);
    }
  });

  // Field type change handler - use dynamic-fields.js function
  $('#assetFieldType').on('change', function() {
    // Use the dynamic-fields.js function
    if (typeof window.DynamicFields !== 'undefined' && window.DynamicFields.handleFieldTypeChange) {
      window.DynamicFields.handleFieldTypeChange(this);
    } else {
      // Fallback to local function
      handleFieldTypeChange($(this).val());
    }
    updateFieldPreview();
  });

  // Add option button - use dynamic-fields.js function
  $(document).on('click', '.add-option-btn', function() {
    if (typeof window.DynamicFields !== 'undefined' && window.DynamicFields.addFieldOption) {
      window.DynamicFields.addFieldOption(this);
    } else {
      // Fallback to local function
      addFieldOption();
    }
    updateFieldPreview();
  });

  // Remove option button - use dynamic-fields.js function
  $(document).on('click', '.remove-option-btn', function() {
    if (typeof window.DynamicFields !== 'undefined' && window.DynamicFields.removeFieldOption) {
      window.DynamicFields.removeFieldOption(this);
    } else {
      // Fallback to local removal
      $(this).closest('.option-item').remove();
    }
    updateFieldPreview();
  });

  // Form input changes for preview
  $('#assetFieldLabel, #assetFieldPlaceholder, #assetFieldHelpText, #assetFieldIsRequired').on('input change', function() {
    updateFieldPreview();
  });

  // Options input changes for preview
  $(document).on('input', '.option-item input', function() {
    updateFieldPreview();
  });

  // Auto-generate name from label
  $('#assetFieldLabel').on('input', function() {
    if (!isEditMode) {
      const label = $(this).val();
      const name = generateNameFromLabel(label);
      $('#assetFieldName').val(name);
    }
  });

  // Form validation on input
  $('#assetFieldName, #assetFieldLabel').on('input', function() {
    validateField(this);
  });
}

/**
 * Handle field type change
 */
function handleFieldTypeChange(type) {
  const optionsSection = document.querySelector('.field-options-section');
  const optionsContainer = document.querySelector('.options-container');

  // Check if elements exist - don't warn if modal is not open
  if (!optionsSection || !optionsContainer) {
    return;
  }

  const needsOptions = ['select', 'radio', 'checkbox'].includes(type);

  if (needsOptions) {
    optionsSection.style.display = 'block';
    if (document.querySelectorAll('.option-item').length === 0) {
      addFieldOption();
      addFieldOption();
    }
  } else {
    optionsSection.style.display = 'none';
    optionsContainer.innerHTML = '';
  }
}

/**
 * Add field option
 */
function addFieldOption() {
  const optionsContainer = document.querySelector('.options-container');

  if (!optionsContainer) {
    return;
  }

  const optionDiv = document.createElement('div');
  optionDiv.className = 'option-item';
  optionDiv.innerHTML = `
    <input type="text" name="options[${optionIndex}][key]" class="option-input" placeholder="Giá trị" required>
    <input type="text" name="options[${optionIndex}][value]" class="option-input" placeholder="Nhãn hiển thị" required>
    <i class="ri-delete-bin-line option-remove remove-option-btn" title="Xóa tùy chọn"></i>
  `;

  optionsContainer.appendChild(optionDiv);
  optionIndex++;

  // Focus on first input
  const firstInput = optionDiv.querySelector('input');
  if (firstInput) {
    firstInput.focus();
  }
}

/**
 * Add field option with data (for editing)
 */
function addFieldOptionWithData(key, value, index) {
  const optionsContainer = document.querySelector('.options-container');

  if (!optionsContainer) {
    return;
  }

  const optionDiv = document.createElement('div');
  optionDiv.className = 'option-item';
  optionDiv.innerHTML = `
    <input type="text" name="options[${index}][key]" class="option-input" placeholder="Giá trị" value="${key}" required>
    <input type="text" name="options[${index}][value]" class="option-input" placeholder="Nhãn hiển thị" value="${value}" required>
    <i class="ri-delete-bin-line option-remove remove-option-btn" title="Xóa tùy chọn"></i>
  `;

  optionsContainer.appendChild(optionDiv);
}

/**
 * Update field preview
 */
function updateFieldPreview() {
  const type = $('#assetFieldType').val();
  const label = $('#assetFieldLabel').val() || 'Field Label';
  const placeholder = $('#assetFieldPlaceholder').val();
  const helpText = $('#assetFieldHelpText').val();
  const isRequired = $('#assetFieldIsRequired').is(':checked');

  if (!type) return;

  let previewHtml = '';
  const requiredMark = isRequired ? ' *' : '';

  // Create preview based on field type
  switch (type) {
    case 'text':
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <input type="text" class="form-control" placeholder="${placeholder || ''}" ${isRequired ? 'required' : ''}>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'number':
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <input type="number" class="form-control" placeholder="${placeholder || ''}" ${isRequired ? 'required' : ''}>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'date':
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <input type="date" class="form-control" ${isRequired ? 'required' : ''}>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'textarea':
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <textarea class="form-control" rows="3" placeholder="${placeholder || ''}" ${isRequired ? 'required' : ''}></textarea>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'select':
      const selectOptions = getOptionsFromForm();
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <select class="form-select" ${isRequired ? 'required' : ''}>
            <option value="">Chọn...</option>
            ${selectOptions.map(opt => `<option value="${opt.key}">${opt.value}</option>`).join('')}
          </select>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'radio':
      const radioOptions = getOptionsFromForm();
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <div>
            ${radioOptions.map((opt, index) => `
              <div class="form-check">
                <input class="form-check-input" type="radio" name="preview_radio" value="${opt.key}" id="preview_radio_${index}">
                <label class="form-check-label" for="preview_radio_${index}">${opt.value}</label>
              </div>
            `).join('')}
          </div>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'checkbox':
      const checkboxOptions = getOptionsFromForm();
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <div>
            ${checkboxOptions.map((opt, index) => `
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${opt.key}" id="preview_checkbox_${index}">
                <label class="form-check-label" for="preview_checkbox_${index}">${opt.value}</label>
              </div>
            `).join('')}
          </div>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;

    case 'file':
      previewHtml = `
        <div class="preview-field">
          <label class="form-label">${label}${requiredMark}</label>
          <input type="file" class="form-control" ${isRequired ? 'required' : ''}>
          ${helpText ? `<div class="preview-help-text">${helpText}</div>` : ''}
        </div>
      `;
      break;
  }

  // Show preview
  let previewContainer = document.querySelector('.field-preview');
  if (!previewContainer) {
    const modalBody = document.querySelector('.modal-body');
    if (!modalBody) {
      return;
    }

    previewContainer = document.createElement('div');
    previewContainer.className = 'field-preview';
    previewContainer.innerHTML = '<div class="field-preview-title">Preview</div><div class="preview-content"></div>';
    modalBody.appendChild(previewContainer);
  }

  const previewContent = previewContainer.querySelector('.preview-content');
  if (previewContent) {
    previewContent.innerHTML = previewHtml;
  }
}

/**
 * Get options from form
 */
function getOptionsFromForm() {
  const options = [];
  document.querySelectorAll('.option-item').forEach(item => {
    const keyInput = item.querySelector('[name*="[key]"]');
    const valueInput = item.querySelector('[name*="[value]"]');

    if (keyInput && valueInput) {
      const key = keyInput.value;
      const value = valueInput.value;
      if (key && value) {
        options.push({ key, value });
      }
    }
  });
  return options;
}

/**
 * Save asset field (create or update)
 */
function saveAssetField() {
  const form = document.getElementById('assetFieldForm');
  const submitBtn = form.querySelector('button[type="submit"]');

  // Show loading state
  setButtonLoading(submitBtn, true);

  // Clear previous validation errors
  clearValidationErrors();

  const formData = new FormData(form);
  const data = {};

  // Convert FormData to object
  for (let [key, value] of formData.entries()) {
    data[key] = value;
  }

  // Convert checkboxes to boolean
  data.is_required = document.getElementById('assetFieldIsRequired').checked;
  data.is_active = document.getElementById('assetFieldIsActive').checked;

  // Process options
  const options = [];
  document.querySelectorAll('.option-item').forEach(item => {
    const keyInput = item.querySelector('[name*="[key]"]');
    const valueInput = item.querySelector('[name*="[value]"]');

    if (keyInput && valueInput) {
      const key = keyInput.value;
      const value = valueInput.value;
      if (key && value) {
        options.push({ key, value });
      }
    }
  });
  data.options = options;

  // Process validation rules
  if (data.validation_rules) {
    data.validation_rules = data.validation_rules.split('|').filter(rule => rule.trim());
  }

  const url = isEditMode ?
    window.assetFieldsRoutes.update(data.id) :
    window.assetFieldsRoutes.store;

  const method = isEditMode ? 'PUT' : 'POST';

  $.ajax({
    url: url,
    type: method,
    data: data,
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(submitBtn, false);

      if (response.success) {
        $('#assetFieldModal').modal('hide');
        assetFieldsTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(submitBtn, false);

      if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;
        displayValidationErrors(errors);
      } else {
        let message = 'Có lỗi xảy ra';

        if (xhr.responseJSON && xhr.responseJSON.message) {
          message = xhr.responseJSON.message;
        }

        showToast('error', message);
      }
    }
  });
}

/**
 * Debug function to check modal and element state
 */
function debugModalState() {
  console.log('=== MODAL DEBUG INFO ===');
  console.log('Modal exists:', !!document.getElementById('assetFieldModal'));
  console.log('Modal is visible:', $('#assetFieldModal').is(':visible'));
  console.log('Modal has show class:', $('#assetFieldModal').hasClass('show'));
  console.log('assetFieldType element exists:', !!document.getElementById('assetFieldType'));
  console.log('assetFieldType in modal:', !!document.querySelector('#assetFieldModal #assetFieldType'));
  console.log('All select elements in modal:', $('#assetFieldModal').find('select').length);
  console.log('=== END MODAL DEBUG ===');
}

/**
 * Edit asset field
 */
function editAssetField(id) {
  isEditMode = true;
  $('#assetFieldModalTitle').text('Sửa field');

  console.log('Starting editAssetField for ID:', id);

  // Ensure modal is properly reset before starting
  resetForm();

  // Show the modal first to ensure content is loaded
  $('#assetFieldModal').modal('show');

  // Wait for modal to be shown, then show loading
  $('#assetFieldModal').one('shown.bs.modal', function() {
    console.log('Modal shown, now starting loading process...');

    // Show loading in modal with callback
    showModalLoading(true, () => {
      console.log('Modal loading shown, now fetching field data...');

        $.ajax({
        url: window.assetFieldsRoutes.show(id),
        type: 'GET',
        success: function(field) {
          console.log('Loaded field data:', field);

          // Hide loading and restore modal content with callback
          showModalLoading(false, () => {
            console.log('Modal content restored, now setting field values...');

            // Verify elements exist before setting values
            const requiredElements = ['assetFieldId', 'assetFieldName', 'assetFieldLabel', 'assetFieldType'];
            const missingElements = requiredElements.filter(id => !document.getElementById(id));

            if (missingElements.length > 0) {
              console.error('Missing elements after content restore:', missingElements);
              console.log('Modal body content:', $('#assetFieldModal .modal-body').html().substring(0, 500));
              showToast('error', 'Lỗi: Không thể tải form. Vui lòng thử lại.');
              return;
            }

            // Set field values
            $('#assetFieldId').val(field.id);
            $('#assetFieldName').val(field.name);
            $('#assetFieldLabel').val(field.label);
            $('#assetFieldType').val(field.type);
            $('#assetFieldPlaceholder').val(field.placeholder);
            $('#assetFieldHelpText').val(field.help_text);
            $('#assetFieldSortOrder').val(field.sort_order);
            $('#assetFieldIsRequired').prop('checked', field.is_required);
            $('#assetFieldIsActive').prop('checked', field.is_active);

            console.log('Field type from database:', field.type);
            console.log('Setting field type select to:', field.type);

            // Handle field type change immediately since content is restored
            handleFieldTypeAfterModalShown(field);

            // Continue with the rest of the logic...
            handleFieldDataLoaded(field);
          });
        },
        error: function(xhr) {
          console.error('Error loading field data:', xhr);
          showModalLoading(false);
          showToast('error', 'Không thể tải thông tin field');
        }
      });
    });
  });
}

/**
 * Handle field type after modal content is restored
 */
function handleFieldTypeAfterModalShown(field, retryCount = 0) {
  const maxRetries = 5;

  console.log(`Attempting to find assetFieldType element (attempt ${retryCount + 1}/${maxRetries})...`);

  // Try multiple ways to find the element
  let selectElement = document.getElementById('assetFieldType');

  if (!selectElement) {
    console.warn('Element not found by ID, trying alternative selectors...');
    selectElement = document.querySelector('#assetFieldModal [name="type"]');
  }

  if (!selectElement) {
    selectElement = document.querySelector('.modal.show [name="type"]');
  }

  if (!selectElement) {
    selectElement = document.querySelector('select[name="type"]');
  }

  console.log('Found select element:', selectElement);

  if (selectElement) {
    // Ensure the value is set before calling the handler
    selectElement.value = field.type;
    console.log('Select element value set to:', selectElement.value);

    // Handle field type change - use dynamic-fields.js function
    if (typeof window.DynamicFields !== 'undefined' && window.DynamicFields.handleFieldTypeChange) {
      console.log('Calling DynamicFields.handleFieldTypeChange with element:', selectElement);
      window.DynamicFields.handleFieldTypeChange(selectElement);
    } else {
      // Fallback to local function
      console.log('Using fallback handleFieldTypeChange');
      handleFieldTypeChange(field.type);
    }

    // Update preview after field type is handled
    updateFieldPreview();

    console.log('✓ Field type handling completed successfully');
  } else {
    if (retryCount < maxRetries) {
      console.warn(`assetFieldType element not found, retrying in 100ms... (${retryCount + 1}/${maxRetries})`);
      setTimeout(() => handleFieldTypeAfterModalShown(field, retryCount + 1), 100);
    } else {
      console.error('✗ assetFieldType select element still not found after all retries');
      console.log('Modal body content:', document.querySelector('#assetFieldModal .modal-body')?.innerHTML?.substring(0, 500));
      showToast('warning', 'Cảnh báo: Một số tính năng có thể không hoạt động đúng');
    }
  }
}

/**
 * Handle field data loaded
 */
function handleFieldDataLoaded(field) {
  // Set validation rules if exists
  if (field.validation_rules) {
    $('#assetFieldValidationRules').val(field.validation_rules);
  }

  // Handle field options for select, radio, checkbox
  if (field.options && field.options.length > 0) {
    const optionsContainer = $('.options-container');
    optionsContainer.empty();

    field.options.forEach(option => {
      addOptionRow(option.value, option.label);
    });

    $('.field-options-section').show();
  }

  // Modal is already shown, no need to show again
  console.log('Field data loaded and form populated successfully');
}

/**
 * Delete asset field
 */
function deleteAssetField(id) {
  deleteAssetFieldId = id;
  $('#deleteModal').modal('show');
}

/**
 * Perform delete asset field
 */
function performDeleteAssetField(id) {
  const deleteBtn = document.getElementById('confirmDeleteBtn');
  setButtonLoading(deleteBtn, true);

  $.ajax({
    url: window.assetFieldsRoutes.destroy(id),
    type: 'DELETE',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(deleteBtn, false);
      $('#deleteModal').modal('hide');

      if (response.success) {
        assetFieldsTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(deleteBtn, false);

      let message = 'Có lỗi xảy ra khi xóa field';

      if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
      }

      showToast('error', message);
      $('#deleteModal').modal('hide');
    }
  });
}

/**
 * Reset form to initial state
 */
function resetForm() {
  isEditMode = false;
  optionIndex = 0;
  $('#assetFieldModalTitle').text('Thêm field mới');
  $('#assetFieldForm')[0].reset();
  $('#assetFieldId').val('');
  $('#assetFieldIsActive').prop('checked', true);

  // Clear options and hide options section - with null checking
  const optionsContainer = document.querySelector('.options-container');
  const optionsSection = document.querySelector('.field-options-section');

  if (optionsContainer) {
    optionsContainer.innerHTML = '';
  }

  if (optionsSection) {
    optionsSection.style.display = 'none';
  }

  // Remove preview - with null checking
  const preview = document.querySelector('.field-preview');
  if (preview) {
    preview.remove();
  }

  clearValidationErrors();
}

/**
 * Generate name from label
 */
function generateNameFromLabel(label) {
  return label
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .substring(0, 50);
}

/**
 * Validate individual field
 */
function validateField(field) {
  const $field = $(field);
  const value = $field.val().trim();

  $field.removeClass('is-invalid is-valid');

  if (field.required && !value) {
    $field.addClass('is-invalid');
    return false;
  }

  if (value) {
    $field.addClass('is-valid');
  }

  return true;
}

/**
 * Display validation errors
 */
function displayValidationErrors(errors) {
  Object.keys(errors).forEach(field => {
    const fieldName = field.charAt(0).toUpperCase() + field.slice(1);
    const $field = $(`#assetField${fieldName}`);
    const $feedback = $field.siblings('.invalid-feedback');

    $field.addClass('is-invalid');

    if ($feedback.length) {
      $feedback.text(errors[field][0]);
    } else {
      $field.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    }
  });
}

/**
 * Clear validation errors
 */
function clearValidationErrors() {
  $('#assetFieldForm .form-control, #assetFieldForm .form-select').removeClass('is-invalid is-valid');
  $('#assetFieldForm .invalid-feedback').remove();
}

/**
 * Set button loading state
 */
function setButtonLoading(button, loading) {
  if (loading) {
    button.classList.add('btn-loading');
    button.disabled = true;
  } else {
    button.classList.remove('btn-loading');
    button.disabled = false;
  }
}

/**
 * Show/hide modal loading state
 */
function showModalLoading(show, callback = null) {
  const modal = document.getElementById('assetFieldModal');
  const modalBody = modal.querySelector('.modal-body');

  if (show) {
    // Ensure modal content is fully loaded before storing
    const requiredElements = ['assetFieldType', 'assetFieldName', 'assetFieldLabel'];
    const hasRequiredElements = requiredElements.every(id => document.getElementById(id));

    if (!hasRequiredElements) {
      console.warn('Modal content not fully loaded, waiting for elements...');
      // Wait a bit for modal content to load, then try again
      setTimeout(() => showModalLoading(show, callback), 100);
      return;
    }

    // Store original content before showing loading
    if (!modal.dataset.originalContent) {
      modal.dataset.originalContent = modalBody.innerHTML;
      console.log('Stored original modal content with', modalBody.innerHTML.length, 'characters');
    }

    const loadingHtml = `
      <div class="d-flex justify-content-center p-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Đang tải...</span>
        </div>
      </div>
    `;

    modalBody.innerHTML = loadingHtml;
    console.log('Modal loading shown');

    if (callback) callback();
  } else {
    // Restore original content
    if (modal.dataset.originalContent) {
      modalBody.innerHTML = modal.dataset.originalContent;
      console.log('Modal content restored with', modal.dataset.originalContent.length, 'characters');

      // Verify restoration was successful
      const assetFieldType = document.getElementById('assetFieldType');
      if (assetFieldType) {
        console.log('✓ Modal content restoration verified - assetFieldType element found');
      } else {
        console.error('✗ Modal content restoration failed - assetFieldType element not found');
        console.log('Modal body content after restore:', modalBody.innerHTML.substring(0, 500));
      }

      // Clear the stored content
      delete modal.dataset.originalContent;

      // Call callback after restoration
      if (callback) {
        setTimeout(callback, 50); // Small delay to ensure DOM is updated
      }
    } else {
      console.warn('No original content to restore');
      if (callback) callback();
    }
  }
}

/**
 * Show toast notification
 */
function showToast(type, message) {
  const toastId = 'toast-' + Date.now();
  const iconClass = type === 'success' ? 'ri-check-line' : 'ri-error-warning-line';
  const bgClass = type === 'error' ? 'danger' : type;

  const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${bgClass} border-0" role="alert">
      <div class="d-flex">
        <div class="toast-body">
          <i class="${iconClass} me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;

  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: type === 'error' ? 5000 : 3000
  });

  toast.show();

  toastElement.addEventListener('hidden.bs.toast', function() {
    toastElement.remove();
  });
}

// Export functions to global scope for DataTable buttons
window.editAssetField = editAssetField;
window.deleteAssetField = deleteAssetField;
window.debugModalState = debugModalState;
