/**
 * Complete Test Script cho Asset Templates
 * Chạy script n<PERSON>y trong Browser Console để test toàn bộ chức năng
 */

console.log('=== COMPLETE ASSET TEMPLATES TEST ===');

// Test function
function runCompleteTest() {
  console.log('\n🧪 Starting Complete Test...');
  
  // 1. Test Dependencies
  console.log('\n1. Testing Dependencies:');
  const dependencies = {
    jQuery: typeof $ !== 'undefined',
    Bootstrap: typeof bootstrap !== 'undefined',
    DataTables: typeof $.fn.DataTable !== 'undefined'
  };
  
  Object.keys(dependencies).forEach(dep => {
    console.log(`${dependencies[dep] ? '✅' : '❌'} ${dep}: ${dependencies[dep] ? 'LOADED' : 'NOT LOADED'}`);
  });
  
  // 2. Test Global Variables
  console.log('\n2. Testing Global Variables:');
  const globals = {
    assetTemplatesRoutes: typeof window.assetTemplatesRoutes !== 'undefined',
    assetFieldsRoute: typeof window.assetFieldsRoute !== 'undefined',
    contractTypes: typeof window.contractTypes !== 'undefined',
    assetTemplatesTable: typeof assetTemplatesTable !== 'undefined'
  };
  
  Object.keys(globals).forEach(global => {
    console.log(`${globals[global] ? '✅' : '❌'} ${global}: ${globals[global] ? 'DEFINED' : 'NOT DEFINED'}`);
  });
  
  // 3. Test Routes
  console.log('\n3. Testing Routes:');
  if (window.assetTemplatesRoutes) {
    const routes = ['index', 'store', 'show', 'update', 'destroy'];
    routes.forEach(route => {
      const exists = typeof window.assetTemplatesRoutes[route] !== 'undefined';
      console.log(`${exists ? '✅' : '❌'} ${route}: ${exists ? 'EXISTS' : 'MISSING'}`);
      
      if (exists && typeof window.assetTemplatesRoutes[route] === 'function') {
        try {
          const testUrl = window.assetTemplatesRoutes[route](1);
          console.log(`   📍 Test URL: ${testUrl}`);
        } catch (e) {
          console.log(`   ❌ Error generating URL: ${e.message}`);
        }
      }
    });
  }
  
  // 4. Test Functions
  console.log('\n4. Testing Functions:');
  const functions = {
    editTemplate: typeof editTemplate === 'function',
    viewTemplate: typeof viewTemplate === 'function',
    deleteTemplate: typeof deleteTemplate === 'function',
    showModalLoading: typeof showModalLoading === 'function',
    showToast: typeof showToast === 'function'
  };
  
  Object.keys(functions).forEach(func => {
    console.log(`${functions[func] ? '✅' : '❌'} ${func}: ${functions[func] ? 'DEFINED' : 'NOT DEFINED'}`);
  });
  
  // 5. Test DOM Elements
  console.log('\n5. Testing DOM Elements:');
  const elements = {
    modal: document.getElementById('assetTemplateModal'),
    table: document.getElementById('assetTemplatesTable'),
    form: document.getElementById('assetTemplateForm')
  };
  
  Object.keys(elements).forEach(elem => {
    const exists = elements[elem] !== null;
    console.log(`${exists ? '✅' : '❌'} ${elem}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);
  });
  
  // 6. Test CSRF Token
  console.log('\n6. Testing CSRF Token:');
  const csrfToken = $('meta[name="csrf-token"]').attr('content');
  console.log(`${csrfToken ? '✅' : '❌'} CSRF Token: ${csrfToken ? 'FOUND' : 'NOT FOUND'}`);
  
  // 7. Test DataTable
  console.log('\n7. Testing DataTable:');
  if (typeof assetTemplatesTable !== 'undefined' && assetTemplatesTable) {
    console.log('✅ DataTable is initialized');
    try {
      const info = assetTemplatesTable.page.info();
      console.log(`   📊 Records: ${info.recordsTotal}, Page: ${info.page + 1}/${Math.ceil(info.recordsTotal / info.length)}`);
    } catch (e) {
      console.log(`   ❌ Error getting DataTable info: ${e.message}`);
    }
  } else {
    console.log('❌ DataTable is not initialized');
  }
  
  // 8. Test Edit Buttons
  console.log('\n8. Testing Edit Buttons:');
  const editButtons = document.querySelectorAll('button[onclick*="editTemplate"]');
  console.log(`${editButtons.length > 0 ? '✅' : '❌'} Edit buttons found: ${editButtons.length}`);
  
  if (editButtons.length > 0) {
    // Get first template ID from button onclick
    const firstButton = editButtons[0];
    const onclickAttr = firstButton.getAttribute('onclick');
    const idMatch = onclickAttr.match(/editTemplate\((\d+)\)/);
    
    if (idMatch) {
      const templateId = idMatch[1];
      console.log(`   📍 First template ID: ${templateId}`);
      
      // Test the edit function
      console.log('\n9. Testing Edit Function:');
      window.testEditTemplate = function() {
        console.log(`🧪 Testing editTemplate(${templateId})...`);
        
        // Override AJAX to capture the request
        const originalAjax = $.ajax;
        $.ajax = function(options) {
          console.log('📡 AJAX Request Details:');
          console.log('   URL:', options.url);
          console.log('   Type:', options.type);
          console.log('   Headers:', options.headers);
          
          // Restore original AJAX
          $.ajax = originalAjax;
          
          // Make the actual request
          return originalAjax.call(this, {
            ...options,
            success: function(response) {
              console.log('✅ AJAX Success!');
              console.log('   Response keys:', Object.keys(response));
              if (response.template) {
                console.log('   Template ID:', response.template.id);
                console.log('   Template Name:', response.template.name);
              }
              if (options.success) options.success(response);
            },
            error: function(xhr, status, error) {
              console.log('❌ AJAX Error!');
              console.log('   Status:', xhr.status);
              console.log('   Status Text:', xhr.statusText);
              console.log('   Response:', xhr.responseText);
              if (options.error) options.error(xhr, status, error);
            }
          });
        };
        
        // Call the edit function
        editTemplate(templateId);
      };
      
      console.log('   🎯 Run window.testEditTemplate() to test edit function');
    }
  }
  
  console.log('\n=== TEST COMPLETE ===');
  console.log('Summary:');
  console.log(`Dependencies: ${Object.values(dependencies).filter(Boolean).length}/${Object.keys(dependencies).length}`);
  console.log(`Global Variables: ${Object.values(globals).filter(Boolean).length}/${Object.keys(globals).length}`);
  console.log(`Functions: ${Object.values(functions).filter(Boolean).length}/${Object.keys(functions).length}`);
  console.log(`DOM Elements: ${Object.values(elements).filter(Boolean).length}/${Object.keys(elements).length}`);
  
  const allPassed = Object.values({...dependencies, ...globals, ...functions}).every(Boolean) && 
                   Object.values(elements).every(elem => elem !== null) && 
                   csrfToken;
  
  console.log(`\n${allPassed ? '🎉 ALL TESTS PASSED!' : '⚠️  SOME TESTS FAILED'}`);
  
  if (!allPassed) {
    console.log('\n🔧 Suggested fixes:');
    if (!dependencies.jQuery) console.log('- Include jQuery library');
    if (!dependencies.Bootstrap) console.log('- Include Bootstrap library');
    if (!dependencies.DataTables) console.log('- Include DataTables library');
    if (!globals.assetTemplatesRoutes) console.log('- Check routes definition in blade template');
    if (!functions.editTemplate) console.log('- Check if asset-templates.js is loaded');
    if (!elements.modal) console.log('- Check if modal HTML exists');
    if (!csrfToken) console.log('- Add CSRF meta tag to page head');
  }
}

// Auto-run test
runCompleteTest();

// Export for manual use
window.runAssetTemplatesTest = runCompleteTest;
