# Hướng Dẫn Phát <PERSON> - Dự Án VPCC

## <PERSON><PERSON><PERSON>
- [0. Tì<PERSON> và <PERSON>](#0-tìm-hiểu-và-khai-thác)
- [1. Mẫu HTML và Template Structure](#1-mẫu-html-và-template-structure)
- [2. <PERSON><PERSON>](#2-tổ-chức-tài-nguyên)
- [3. Triển Khai JavaScript/CSS](#3-triển-khai-javascriptcss)
- [4. Triển Khai DataTables](#4-triển-khai-datatables)
- [5. Th<PERSON><PERSON>ần Biểu Mẫu](#5-thành-phần-biểu-mẫu)
- [6. Debugging và Troubleshooting](#6-debugging-và-troubleshooting)

---

## 0. Tìm Hiểu và Khai Thác

### Phân Tích Cấu Trúc Hiện Có

<PERSON>rước khi phát triển t<PERSON>h nă<PERSON>ớ<PERSON>, h<PERSON><PERSON> nghiê<PERSON> cứu các module hiệ<PERSON> có:

#### C<PERSON>u Trúc Views
```
resources/views/
├── layouts/layoutMaster.blade.php (Layout chính)
├── asset-fields/index.blade.php (Ví dụ CRUD)
├── asset-templates/index.blade.php
├── contract-types/index.blade.php
└── template/ (Templates tham khảo)
```

#### Cấu Trúc JavaScript
```
resources/js/
├── common-helpers.js (Utilities chung)
├── asset-fields.js (CRUD logic)
├── asset-templates.js
├── contract-types.js
└── datatables-config.js (DataTables config)
```

#### Cấu Trúc CSS
```
resources/css/
├── app.css (CSS chính)
├── asset-fields.css (Module-specific CSS)
├── asset-templates.css
└── contract-types.css
```

### Cách Phân Tích Code Hiện Có

1. **Xem cấu trúc Blade template:**
```bash
# Xem template chính
cat resources/views/asset-fields/index.blade.php
```

2. **Phân tích JavaScript patterns:**
```bash
# Xem logic CRUD
cat resources/js/asset-fields.js
```

3. **Hiểu cách tổ chức CSS:**
```bash
# Xem styling patterns
cat resources/css/asset-fields.css
```

---

## 1. Mẫu HTML và Template Structure

### Layout Master Pattern

Tất cả views phải extend từ `layouts/layoutMaster`:

```blade
@extends('layouts/layoutMaster')

@section('title', 'Tên Module')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

@section('page-style')
@vite(['resources/css/module-name.css'])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('page-script')
@vite(['resources/js/module-name.js'])
@endsection

@section('content')
<!-- Nội dung trang -->
@endsection
```

### Cấu Trúc CRUD Standard

#### Card Container với Header
```blade
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-icon-name me-2"></i>
          Tiêu đề Module
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('permission.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#moduleModal">
              <i class="ri-add-line me-1"></i>
              Thêm mới
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="moduleTable" class="datatables-module table">
          <thead>
            <tr>
              <th>Cột 1</th>
              <th>Cột 2</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>
```

#### Modal Structure
```blade
<!-- Module Modal -->
<div class="modal fade" id="moduleModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="moduleModalTitle">Thêm mới</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="moduleForm">
        <div class="modal-body">
          <input type="hidden" id="moduleId" name="id">
          <!-- Form fields -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
```

### Routes Configuration trong Blade

```blade
@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.moduleRoutes = {
  index: '{{ route("module.index") }}',
  store: '{{ route("module.store") }}',
  show: function(id) { return `/module/${id}`; },
  update: function(id) { return `/module/${id}`; },
  destroy: function(id) { return `/module/${id}`; }
};
</script>
@endpush
```

---

## 2. Tổ Chức Tài Nguyên

### Quy Tắc Đặt Tên File

#### JavaScript Files
- **Module-specific:** `resources/js/module-name.js`
- **Common utilities:** `resources/js/common-helpers.js`
- **Configuration:** `resources/js/datatables-config.js`

#### CSS Files
- **Module-specific:** `resources/css/module-name.css`
- **Global styles:** `resources/css/app.css`

### Cấu Trúc Thư Mục Assets

```
resources/
├── assets/ (Vendor assets - KHÔNG CHỈNH SỬA)
│   ├── vendor/libs/datatables-bs5/
│   ├── vendor/libs/select2/
│   └── js/ (Template JS files)
├── js/ (Custom JavaScript)
│   ├── common-helpers.js
│   ├── module-name.js
│   └── datatables-config.js
└── css/ (Custom CSS)
    ├── app.css
    └── module-name.css
```

### Vite Configuration

File `vite.config.js` đã được cấu hình để tự động include:

```javascript
// Custom JS files for CRUD modules
'resources/js/contract-types.js',
'resources/js/asset-fields.js',
'resources/js/asset-templates.js',

// Custom CSS files for CRUD modules  
'resources/css/contract-types.css',
'resources/css/asset-fields.css',
'resources/css/asset-templates.css',
```

**Lưu ý:** Khi tạo module mới, cần thêm vào `vite.config.js`:

```javascript
// Thêm vào input array
'resources/js/new-module.js',
'resources/css/new-module.css',
```

---

## 3. Triển Khai JavaScript/CSS

### JavaScript Module Pattern

Mỗi module JavaScript phải tuân theo pattern sau:

```javascript
/**
 * Module Name Management JavaScript
 * Handles CRUD operations with AJAX
 */

// Global variables
let moduleTable;
let deleteModuleId = null;
let isEditMode = false;

// Initialize when DOM is ready
$(document).ready(function() {
  // Check if routes are available
  if (!checkRoutes(window.moduleRoutes, 'Module Name')) {
    return;
  }

  initializeDataTable();
  initializeEventHandlers();
});

/**
 * Initialize DataTable
 */
function initializeDataTable() {
  moduleTable = $('#moduleTable').DataTable({
    processing: true,
    ajax: {
      url: window.moduleRoutes.index,
      type: 'GET',
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      { data: 'name', name: 'name' },
      { data: 'status', name: 'status' },
      { data: 'actions', name: 'actions', orderable: false, searchable: false }
    ],
    order: [[0, 'asc']],
    pageLength: 25,
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json',
      processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Đang tải...</span></div></div>'
    }
  });
}

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
  // Form submission
  $('#moduleForm').on('submit', function(e) {
    e.preventDefault();
    saveModule();
  });

  // Modal reset when hidden
  $('#moduleModal').on('hidden.bs.modal', function() {
    resetForm();
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    if (deleteModuleId) {
      performDeleteModule(deleteModuleId);
    }
  });
}
```

### Common Helpers Usage

Sử dụng các hàm tiện ích từ `common-helpers.js`:

```javascript
// Show notifications
showToast('success', 'Thành công!');
showToast('error', 'Có lỗi xảy ra!');

// Button loading states
setButtonLoading(submitBtn, true);  // Show loading
setButtonLoading(submitBtn, false); // Hide loading

// Form validation
clearValidationErrors();
displayValidationErrors(errors);

// Route checking
if (!checkRoutes(window.moduleRoutes, 'Module Name')) {
  return;
}
```

### CSS Organization

#### Module-specific CSS Structure

```css
/* resources/css/module-name.css */

/* Module-specific styles */
.module-container {
  /* Container styles */
}

.module-form {
  /* Form styles */
}

.module-table {
  /* Table customizations */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .module-container {
    /* Mobile styles */
  }
}
```

### Asset Loading trong Blade

```blade
<!-- Vendor Styles -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss'
])
@endsection

<!-- Page Styles -->
@section('page-style')
@vite(['resources/css/module-name.css'])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
@vite([
  'resources/js/common-helpers.js',
  'resources/js/module-name.js'
])
@endsection
```

---

## 4. Triển Khai DataTables

### Client-side DataTables với AJAX

Dự án sử dụng **client-side DataTables** với AJAX data loading thay vì server-side processing:

```javascript
function initializeDataTable() {
  moduleTable = $('#moduleTable').DataTable({
    processing: true,
    ajax: {
      url: window.moduleRoutes.index,
      type: 'GET',
      dataSrc: 'data', // Laravel resource collection
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      { 
        data: 'name', 
        name: 'name',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      { 
        data: 'status', 
        name: 'status',
        render: function(data, type, row) {
          const badgeClass = data ? 'bg-success' : 'bg-secondary';
          const text = data ? 'Hoạt động' : 'Không hoạt động';
          return `<span class="badge ${badgeClass}">${text}</span>`;
        }
      },
      { 
        data: 'actions', 
        name: 'actions', 
        orderable: false, 
        searchable: false,
        render: function(data, type, row) {
          return `
            <div class="dropdown">
              <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                <i class="ri-more-2-line"></i>
              </button>
              <div class="dropdown-menu">
                <a class="dropdown-item" href="javascript:void(0);" onclick="editModule(${row.id})">
                  <i class="ri-pencil-line me-1"></i> Sửa
                </a>
                <a class="dropdown-item" href="javascript:void(0);" onclick="deleteModule(${row.id})">
                  <i class="ri-delete-bin-7-line me-1"></i> Xóa
                </a>
              </div>
            </div>
          `;
        }
      }
    ],
    order: [[0, 'asc']],
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json',
      processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Đang tải...</span></div></div>',
      emptyTable: '<div class="text-center py-4"><i class="ri-file-list-line ri-48px text-muted mb-3"></i><br>Chưa có dữ liệu</div>'
    },
    responsive: true,
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>'
  });
}
```

### Laravel Controller Response Format

Controller phải trả về format phù hợp với DataTables:

```php
public function index(Request $request)
{
    $modules = Module::select(['id', 'name', 'status', 'created_at'])
        ->orderBy('name')
        ->get();

    $data = $modules->map(function ($module) {
        return [
            'id' => $module->id,
            'name' => $module->name,
            'status' => $module->status,
            'created_at' => $module->created_at->format('d/m/Y'),
            'actions' => '' // Will be rendered by DataTables
        ];
    });

    return response()->json([
        'data' => $data
    ]);
}
```

### Filtering và Search

```javascript
// Global search với debounce
$('#globalSearch').on('keyup', debounce(function() {
  moduleTable.search(this.value).draw();
}, 500));

// Column-specific filters
$('#statusFilter').on('change', function() {
  const value = this.value;
  moduleTable.column(1).search(value).draw();
});

// Clear all filters
$('#clearFilters').on('click', function() {
  $('#globalSearch, #statusFilter').val('');
  moduleTable.search('').columns().search('').draw();
  showToast('info', 'Đã xóa tất cả bộ lọc');
});
```

### Export Functionality

```javascript
// Export buttons configuration
buttons: [
  {
    extend: 'excel',
    text: '<i class="ri-file-excel-line me-1"></i>Excel',
    className: 'btn btn-success btn-sm',
    exportOptions: {
      columns: [0, 1, 2] // Exclude actions column
    }
  },
  {
    extend: 'pdf',
    text: '<i class="ri-file-pdf-line me-1"></i>PDF',
    className: 'btn btn-danger btn-sm',
    exportOptions: {
      columns: [0, 1, 2]
    }
  }
]
```

---

## 5. Thành Phần Biểu Mẫu

### Select2 Implementation

Tất cả dropdown select phải sử dụng Select2:

#### Basic Select2 Setup

```javascript
// Initialize Select2
$('#selectElement').select2({
  placeholder: 'Chọn một tùy chọn',
  allowClear: true,
  width: '100%'
});
```

#### Select2 với AJAX Data Loading

```javascript
$('#dynamicSelect').select2({
  ajax: {
    url: '/api/search-endpoint',
    dataType: 'json',
    delay: 250,
    data: function (params) {
      return {
        q: params.term,
        page: params.page || 1
      };
    },
    processResults: function (data, params) {
      params.page = params.page || 1;

      return {
        results: data.data.map(item => ({
          id: item.id,
          text: item.name
        })),
        pagination: {
          more: data.current_page < data.last_page
        }
      };
    },
    cache: true
  },
  placeholder: 'Tìm kiếm...',
  minimumInputLength: 2,
  width: '100%'
});
```

#### Select2 trong Modal

```javascript
// Initialize Select2 when modal is shown
$('#moduleModal').on('shown.bs.modal', function() {
  $('#selectInModal').select2({
    placeholder: 'Chọn tùy chọn',
    dropdownParent: $('#moduleModal'), // Important for modals
    width: '100%'
  });
});

// Destroy Select2 when modal is hidden
$('#moduleModal').on('hidden.bs.modal', function() {
  $('#selectInModal').select2('destroy');
});
```

### Form Validation

#### Client-side Validation

```javascript
function validateForm() {
  let isValid = true;

  // Clear previous errors
  clearValidationErrors();

  // Required fields validation
  const requiredFields = ['name', 'email', 'type'];
  requiredFields.forEach(fieldName => {
    const field = document.getElementById(fieldName);
    if (!field.value.trim()) {
      field.classList.add('is-invalid');
      isValid = false;
    }
  });

  // Email validation
  const emailField = document.getElementById('email');
  if (emailField.value && !isValidEmail(emailField.value)) {
    emailField.classList.add('is-invalid');
    isValid = false;
  }

  return isValid;
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

#### Server-side Validation Error Handling

```javascript
// Handle validation errors from Laravel
function handleValidationErrors(xhr) {
  if (xhr.status === 422) {
    const errors = xhr.responseJSON.errors;
    displayValidationErrors(errors);
  } else {
    let message = 'Có lỗi xảy ra';
    if (xhr.responseJSON && xhr.responseJSON.message) {
      message = xhr.responseJSON.message;
    }
    showToast('error', message);
  }
}
```

### Form Submission Pattern

```javascript
function saveModule() {
  const form = document.getElementById('moduleForm');
  const submitBtn = form.querySelector('button[type="submit"]');

  // Validate form
  if (!validateForm()) {
    return;
  }

  // Show loading state
  setButtonLoading(submitBtn, true);
  clearValidationErrors();

  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());

  // Convert checkboxes to boolean
  data.is_active = document.getElementById('isActive').checked;

  const url = isEditMode ?
    window.moduleRoutes.update(data.id) :
    window.moduleRoutes.store;

  const method = isEditMode ? 'PUT' : 'POST';

  $.ajax({
    url: url,
    type: method,
    data: data,
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(submitBtn, false);

      if (response.success) {
        $('#moduleModal').modal('hide');
        moduleTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(submitBtn, false);
      handleValidationErrors(xhr);
    }
  });
}
```

---

## 6. Debugging và Troubleshooting

### Các Vấn Đề Thường Gặp

#### 1. Trang Hiển thị Loading Nhưng Không Có Phản Hồi

**Triệu chứng:** Click button/link, trang hiển thị loading spinner nhưng không có gì xảy ra.

**Cách debug:**

1. **Kiểm tra Console Browser:**
```javascript
// Mở Developer Tools (F12) -> Console tab
// Tìm các lỗi JavaScript màu đỏ
```

2. **Kiểm tra Network Tab:**
```javascript
// Developer Tools -> Network tab
// Reload trang và thực hiện action
// Xem các request có status code đỏ (4xx, 5xx)
```

3. **Kiểm tra AJAX Errors:**
```javascript
// Thêm error handling chi tiết
$.ajax({
  url: url,
  type: 'POST',
  data: data,
  success: function(response) {
    console.log('Success:', response);
  },
  error: function(xhr, status, error) {
    console.error('AJAX Error:', {
      status: xhr.status,
      statusText: xhr.statusText,
      responseText: xhr.responseText,
      error: error
    });
  }
});
```

#### 2. DataTables Không Load Dữ Liệu

**Cách debug:**

1. **Kiểm tra AJAX URL:**
```javascript
// Verify routes are defined
console.log('Routes:', window.moduleRoutes);

// Check AJAX response
moduleTable = $('#moduleTable').DataTable({
  ajax: {
    url: window.moduleRoutes.index,
    error: function(xhr, error, thrown) {
      console.error('DataTable Error:', {
        xhr: xhr,
        error: error,
        thrown: thrown,
        responseText: xhr.responseText
      });
    }
  }
});
```

2. **Kiểm tra Response Format:**
```javascript
// Controller phải trả về đúng format
{
  "data": [
    {"id": 1, "name": "Item 1"},
    {"id": 2, "name": "Item 2"}
  ]
}
```

#### 3. Modal Không Hiển thị Hoặc Không Đóng

**Cách debug:**

1. **Kiểm tra Bootstrap Modal:**
```javascript
// Check if Bootstrap is loaded
console.log('Bootstrap:', typeof bootstrap);

// Manual modal control
$('#moduleModal').modal('show');
$('#moduleModal').modal('hide');
```

2. **Kiểm tra Modal Events:**
```javascript
$('#moduleModal').on('show.bs.modal', function() {
  console.log('Modal showing');
});

$('#moduleModal').on('shown.bs.modal', function() {
  console.log('Modal shown');
});

$('#moduleModal').on('hidden.bs.modal', function() {
  console.log('Modal hidden');
});
```

#### 4. Select2 Không Hoạt Động

**Cách debug:**

1. **Kiểm tra Select2 Library:**
```javascript
// Check if Select2 is loaded
console.log('Select2:', typeof $.fn.select2);

// Initialize with error handling
try {
  $('#selectElement').select2({
    placeholder: 'Test'
  });
} catch (error) {
  console.error('Select2 Error:', error);
}
```

2. **Kiểm tra trong Modal:**
```javascript
// Select2 in modal needs dropdownParent
$('#selectInModal').select2({
  dropdownParent: $('#moduleModal')
});
```

### Debug Tools và Techniques

#### 1. Laravel Debug

```php
// Enable debug mode in .env
APP_DEBUG=true

// Log debugging info
\Log::info('Debug data:', $data);

// Dump and die
dd($variable);

// Dump without die
dump($variable);
```

#### 2. JavaScript Debug

```javascript
// Console logging
console.log('Variable:', variable);
console.error('Error:', error);
console.table(arrayData);

// Debugger breakpoint
debugger;

// Check if function exists
if (typeof functionName === 'function') {
  functionName();
} else {
  console.error('Function not found:', 'functionName');
}
```

#### 3. Network Debugging

```javascript
// Monitor AJAX requests
$(document).ajaxStart(function() {
  console.log('AJAX request started');
});

$(document).ajaxComplete(function(event, xhr, settings) {
  console.log('AJAX completed:', {
    url: settings.url,
    status: xhr.status,
    response: xhr.responseText
  });
});

$(document).ajaxError(function(event, xhr, settings, error) {
  console.error('AJAX error:', {
    url: settings.url,
    status: xhr.status,
    error: error,
    response: xhr.responseText
  });
});
```

### Performance Optimization

#### 1. JavaScript Optimization

```javascript
// Use event delegation for dynamic content
$(document).on('click', '.dynamic-button', function() {
  // Handle click
});

// Debounce search inputs
const debouncedSearch = debounce(function(query) {
  // Perform search
}, 300);

$('#searchInput').on('input', function() {
  debouncedSearch(this.value);
});
```

#### 2. DataTables Optimization

```javascript
// Use deferRender for large datasets
$('#table').DataTable({
  deferRender: true,
  pageLength: 25,
  lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
});

// Destroy and recreate for dynamic content
if ($.fn.DataTable.isDataTable('#table')) {
  $('#table').DataTable().destroy();
}
$('#table').DataTable(config);
```

---

## Best Practices Summary

### 1. Code Organization
- Một file JS/CSS cho mỗi module
- Sử dụng common-helpers.js cho utilities chung
- Tuân theo naming conventions

### 2. Error Handling
- Luôn có error handling cho AJAX calls
- Hiển thị thông báo lỗi user-friendly
- Log errors để debug

### 3. Performance
- Sử dụng debounce cho search inputs
- Lazy load data khi cần thiết
- Optimize DataTables configuration

### 4. User Experience
- Loading states cho tất cả actions
- Validation feedback rõ ràng
- Responsive design

### 5. Security
- Luôn sử dụng CSRF tokens
- Validate dữ liệu ở cả client và server
- Sanitize user inputs

---

## Troubleshooting Checklist

Khi gặp vấn đề, hãy kiểm tra theo thứ tự:

1. ✅ **Browser Console** - Có lỗi JavaScript không?
2. ✅ **Network Tab** - Request có thành công không?
3. ✅ **Laravel Logs** - Có lỗi server-side không?
4. ✅ **Routes** - Routes có được định nghĩa đúng không?
5. ✅ **CSRF Token** - Token có được include không?
6. ✅ **Permissions** - User có quyền thực hiện action không?
7. ✅ **Dependencies** - Các thư viện có được load đúng không?

Với hướng dẫn này, bạn có thể phát triển các tính năng mới một cách nhất quán và debug hiệu quả các vấn đề phát sinh.
