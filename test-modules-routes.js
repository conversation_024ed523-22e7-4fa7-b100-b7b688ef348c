/**
 * Test script to verify all modules routes are properly loaded
 * Run this in browser console after loading any module page
 */

function testModuleRoutes() {
    console.log('🧪 Testing Module Routes Loading...\n');
    
    const modules = [
        {
            name: 'Contract Types',
            routesVar: 'contractTypesRoutes',
            url: '/contract-types',
            expectedRoutes: ['index', 'store', 'show', 'update', 'destroy']
        },
        {
            name: 'Asset Fields', 
            routesVar: 'assetFieldsRoutes',
            url: '/asset-fields',
            expectedRoutes: ['index', 'store', 'show', 'update', 'destroy']
        },
        {
            name: 'Asset Templates',
            routesVar: 'assetTemplatesRoutes', 
            url: '/asset-templates',
            expectedRoutes: ['index', 'store', 'show', 'update', 'destroy']
        }
    ];
    
    let allPassed = true;
    
    modules.forEach(module => {
        console.log(`\n📋 Testing ${module.name}:`);
        
        // Check if routes variable exists
        if (typeof window[module.routesVar] === 'undefined') {
            console.error(`❌ ${module.routesVar} is not defined`);
            allPassed = false;
            return;
        }
        
        console.log(`✅ ${module.routesVar} is defined`);
        
        const routes = window[module.routesVar];
        
        // Check each expected route
        module.expectedRoutes.forEach(routeName => {
            if (typeof routes[routeName] === 'undefined') {
                console.error(`❌ Route '${routeName}' is missing`);
                allPassed = false;
            } else {
                console.log(`✅ Route '${routeName}' exists`);
                
                // Test function routes
                if (typeof routes[routeName] === 'function') {
                    try {
                        const testUrl = routes[routeName](1);
                        console.log(`   📍 Test URL: ${testUrl}`);
                    } catch (e) {
                        console.error(`❌ Route function '${routeName}' error:`, e);
                        allPassed = false;
                    }
                } else {
                    console.log(`   📍 Static URL: ${routes[routeName]}`);
                }
            }
        });
    });
    
    // Test checkRoutes function
    console.log('\n🔧 Testing checkRoutes function:');
    if (typeof checkRoutes === 'function') {
        console.log('✅ checkRoutes function is available');
        
        // Test with valid routes
        const testResult1 = checkRoutes({test: 'value'}, 'Test Module');
        console.log(`✅ checkRoutes with valid object: ${testResult1}`);
        
        // Test with undefined routes
        const testResult2 = checkRoutes(undefined, 'Test Module');
        console.log(`✅ checkRoutes with undefined: ${testResult2}`);
        
    } else {
        console.error('❌ checkRoutes function is not available');
        allPassed = false;
    }
    
    // Test DataTables initialization
    console.log('\n📊 Testing DataTables:');
    const tables = ['contractTypesTable', 'assetFieldsTable', 'assetTemplatesTable'];
    
    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        if (table) {
            console.log(`✅ Table ${tableId} exists in DOM`);
            
            // Check if DataTable is initialized
            if ($.fn.DataTable.isDataTable(`#${tableId}`)) {
                console.log(`✅ DataTable ${tableId} is initialized`);
            } else {
                console.log(`⚠️ DataTable ${tableId} is not initialized (may be normal if not on that page)`);
            }
        }
    });
    
    // Final result
    console.log('\n' + '='.repeat(50));
    if (allPassed) {
        console.log('🎉 ALL TESTS PASSED! Routes loading is working correctly.');
    } else {
        console.log('❌ SOME TESTS FAILED! Check the errors above.');
    }
    console.log('='.repeat(50));
    
    return allPassed;
}

// Auto-run test if in browser
if (typeof window !== 'undefined') {
    // Wait for DOM and scripts to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testModuleRoutes, 1000);
        });
    } else {
        setTimeout(testModuleRoutes, 1000);
    }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = testModuleRoutes;
}
